import os
import re

def find_performance_issues():
    """查找可能的性能问题"""
    
    # 要检查的文件
    files_to_check = [
        '/mnt/disk2/surui/BoostingVLA/robotprm/src/trainers/rtg_trainer.py',
        '/mnt/disk2/surui/BoostingVLA/robotprm/src/trainers/irl_trainer.py',
        '/mnt/disk2/surui/BoostingVLA/robotprm/scripts/train_unified.py',
        '/mnt/disk2/surui/BoostingVLA/robotprm/src/losses/',
    ]
    
    # 性能杀手模式
    performance_killers = [
        r'\.item\(\)',           # GPU同步
        r'\.cpu\(\)',            # GPU到CPU传输
        r'\.numpy\(\)',          # GPU到CPU传输
        r'torch\.cuda\.synchronize\(\)',  # 显式同步
        r'dist\.barrier\(\)',    # 分布式同步
        r'print.*loss',          # 可能包含.item()的打印
        r'log.*loss',            # 可能包含.item()的日志
        r'wandb\.log',           # 日志记录
        r'swanlab\.log',         # 日志记录
    ]
    
    for file_path in files_to_check:
        if os.path.isfile(file_path):
            print(f"\n=== 检查文件: {file_path} ===")
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                for i, line in enumerate(lines, 1):
                    for pattern in performance_killers:
                        if re.search(pattern, line):
                            print(f"Line {i}: {line.strip()}")
                            print(f"  ⚠️  匹配模式: {pattern}")
            except Exception as e:
                print(f"无法读取文件: {e}")
        elif os.path.isdir(file_path):
            # 检查目录中的所有Python文件
            for root, dirs, files in os.walk(file_path):
                for file in files:
                    if file.endswith('.py'):
                        full_path = os.path.join(root, file)
                        print(f"\n=== 检查文件: {full_path} ===")
                        try:
                            with open(full_path, 'r', encoding='utf-8') as f:
                                lines = f.readlines()
                            
                            for i, line in enumerate(lines, 1):
                                for pattern in performance_killers:
                                    if re.search(pattern, line):
                                        print(f"Line {i}: {line.strip()}")
                                        print(f"  ⚠️  匹配模式: {pattern}")
                        except Exception as e:
                            print(f"无法读取文件: {e}")

if __name__ == "__main__":
    find_performance_issues()

"""
(prm) (base) surui@machine1:~/BoostingVLA$ /mnt/disk2/surui/miniconda3/envs/prm/bin/python /mnt/disk2/surui/BoostingVLA/find_performance_issues.py

=== 检查文件: /mnt/disk2/surui/BoostingVLA/robotprm/src/trainers/rtg_trainer.py ===
Line 109: print(f"📊 损失类型: {config.loss_function}")
  ⚠️  匹配模式: print.*loss
Line 349: 'train/total_loss': total_loss.item(),
  ⚠️  匹配模式: \.item\(\)
Line 350: 'train/gcl_loss': gcl_loss.item(),
  ⚠️  匹配模式: \.item\(\)
Line 351: 'train/preference_loss': preference_loss.item(),
  ⚠️  匹配模式: \.item\(\)
Line 352: 'train/rtg_loss': rtg_loss.item(),
  ⚠️  匹配模式: \.item\(\)
Line 354: 'train/grad_norm': grad_norm.item() if grad_norm is not None else 0.0,
  ⚠️  匹配模式: \.item\(\)
Line 358: main_loss = preference_loss.item() if self.config.preference_weight > 0 else gcl_loss.item()
  ⚠️  匹配模式: \.item\(\)
Line 362: 'v_loss': rtg_loss.item(),
  ⚠️  匹配模式: \.item\(\)
Line 363: 'advantage': expert_rtg_predictions.mean().item() if expert_rtg_predictions is not None else 0.0,
  ⚠️  匹配模式: \.item\(\)
Line 364: 'reward_mean': rtg_labels.mean().item() if rtg_labels is not None else 0.0,
  ⚠️  匹配模式: \.item\(\)
Line 412: total_reward += current_reward.sum().item()
  ⚠️  匹配模式: \.item\(\)
Line 413: total_rtg_reward += current_rtg_reward.sum().item()
  ⚠️  匹配模式: \.item\(\)

=== 检查文件: /mnt/disk2/surui/BoostingVLA/robotprm/src/trainers/irl_trainer.py ===
Line 99: print(f"📊 损失类型: {config.loss_function}")
  ⚠️  匹配模式: print.*loss
Line 293: 'train/total_loss': total_loss.item(),
  ⚠️  匹配模式: \.item\(\)
Line 294: 'train/gcl_loss': gcl_loss.item(),
  ⚠️  匹配模式: \.item\(\)
Line 295: 'train/preference_loss': preference_loss.item(),
  ⚠️  匹配模式: \.item\(\)
Line 296: 'train/rtg_loss': rtg_loss.item(),
  ⚠️  匹配模式: \.item\(\)
Line 298: 'train/grad_norm': grad_norm.item() if grad_norm is not None else 0.0,
  ⚠️  匹配模式: \.item\(\)
Line 302: main_loss = preference_loss.item() if self.config.preference_weight > 0 else gcl_loss.item()
  ⚠️  匹配模式: \.item\(\)
Line 306: 'v_loss': rtg_loss.item(),
  ⚠️  匹配模式: \.item\(\)
Line 307: 'advantage': expert_predictions.mean().item() if expert_predictions is not None else 0.0,
  ⚠️  匹配模式: \.item\(\)
Line 308: 'reward_mean': rtg_labels.mean().item() if rtg_labels is not None else 0.0,
  ⚠️  匹配模式: \.item\(\)
Line 349: total_reward += current_reward.sum().item()
  ⚠️  匹配模式: \.item\(\)

=== 检查文件: /mnt/disk2/surui/BoostingVLA/robotprm/scripts/train_unified.py ===
Line 215: dist.barrier()
  ⚠️  匹配模式: dist\.barrier\(\)
Line 414: swanlab.log(log_data)
  ⚠️  匹配模式: swanlab\.log
Line 423: dist.barrier()
  ⚠️  匹配模式: dist\.barrier\(\)
Line 458: swanlab.log(aggregated_val_stats)
  ⚠️  匹配模式: swanlab\.log
Line 484: swanlab.log(epoch_log_data)
  ⚠️  匹配模式: swanlab\.log

=== 检查文件: /mnt/disk2/surui/BoostingVLA/robotprm/src/losses/loss_config.py ===

=== 检查文件: /mnt/disk2/surui/BoostingVLA/robotprm/src/losses/__init__.py ===

=== 检查文件: /mnt/disk2/surui/BoostingVLA/robotprm/src/losses/rtg_expectile_loss.py ===
Line 56: 'rtg_expectile_loss': expectile_loss.item(),
  ⚠️  匹配模式: \.item\(\)
Line 57: 'mae': abs_residuals.mean().item(),
  ⚠️  匹配模式: \.item\(\)
Line 58: 'mse': squared_residuals.mean().item(),
  ⚠️  匹配模式: \.item\(\)
Line 59: 'rmse': torch.sqrt(squared_residuals.mean()).item(),
  ⚠️  匹配模式: \.item\(\)
Line 60: 'predictions_mean': predictions.mean().item(),
  ⚠️  匹配模式: \.item\(\)
Line 61: 'targets_mean': targets.mean().item(),
  ⚠️  匹配模式: \.item\(\)
Line 62: 'residuals_mean': residuals.mean().item(),
  ⚠️  匹配模式: \.item\(\)

=== 检查文件: /mnt/disk2/surui/BoostingVLA/robotprm/src/losses/gcl_loss.py ===
Line 85: 'gcl_loss': gcl_loss.item(),
  ⚠️  匹配模式: \.item\(\)
Line 86: 'cost_demo_mean': cost_demo_mean.item(),
  ⚠️  匹配模式: \.item\(\)
Line 87: 'log_expectation': log_expectation.item(),
  ⚠️  匹配模式: \.item\(\)
Line 88: 'advantage': expert_rewards.mean().item() - selected_sample_rewards.mean().item(),
  ⚠️  匹配模式: \.item\(\)
Line 89: 'expert_reward_mean': expert_rewards.mean().item(),
  ⚠️  匹配模式: \.item\(\)
Line 90: 'expert_reward_std': expert_rewards.std().item(),
  ⚠️  匹配模式: \.item\(\)
Line 91: 'sample_reward_mean': selected_sample_rewards.mean().item(),
  ⚠️  匹配模式: \.item\(\)
Line 92: 'prob_mean': selected_sample_probs.mean().item(),
  ⚠️  匹配模式: \.item\(\)
Line 94: 'exp_negative_cost_max': exp_negative_cost.max().item(),
  ⚠️  匹配模式: \.item\(\)
Line 95: 'exp_negative_cost_over_prob_max': exp_negative_cost_over_prob.max().item(),
  ⚠️  匹配模式: \.item\(\)
Line 96: 'exp_negative_cost_over_prob_mean': exp_negative_cost_over_prob.mean().item(),
  ⚠️  匹配模式: \.item\(\)
Line 97: 'negative_costs_clamped_max': negative_costs_clamped.max().item(),
  ⚠️  匹配模式: \.item\(\)

=== 检查文件: /mnt/disk2/surui/BoostingVLA/robotprm/src/losses/bradley_terry_loss.py ===
Line 102: 'bt_loss': final_loss.item(),
  ⚠️  匹配模式: \.item\(\)
Line 103: 'good_reward_mean': good_reward.mean().item(),        # 好动作的平均reward
  ⚠️  匹配模式: \.item\(\)
Line 104: 'bad_reward_mean': bad_reward.mean().item(),          # 坏动作的平均reward
  ⚠️  匹配模式: \.item\(\)
Line 105: 'good_rmse_mean': good_rmse.mean().item(),            # 好动作的平均RMSE（应该较小）
  ⚠️  匹配模式: \.item\(\)
Line 106: 'bad_rmse_mean': bad_rmse.mean().item(),              # 坏动作的平均RMSE（应该较大）
  ⚠️  匹配模式: \.item\(\)
Line 107: 'sample_reward_mean': sample_rewards.mean().item(),   # 所有样本的平均reward
  ⚠️  匹配模式: \.item\(\)
Line 109: 'preference_strength_real': delta_star.mean().item(),     # 真实偏好强度 Δ*
  ⚠️  匹配模式: \.item\(\)
Line 110: 'preference_strength_pred': delta_hat.mean().item(),      # 预测偏好强度 Δ̂
  ⚠️  匹配模式: \.item\(\)
Line 111: 'margin_term_mean': margin_term.mean().item(),            # 边际项大小
  ⚠️  匹配模式: \.item\(\)
Line 112: 'reward_advantage': good_reward.mean().item() - bad_reward.mean().item(),  # 期望>0
  ⚠️  匹配模式: \.item\(\)
Line 113: 'rmse_advantage': bad_rmse.mean().item() - good_rmse.mean().item(),        # 期望>0
  ⚠️  匹配模式: \.item\(\)

=== 检查文件: /mnt/disk2/surui/BoostingVLA/robotprm/src/losses/base_loss.py ===

"""