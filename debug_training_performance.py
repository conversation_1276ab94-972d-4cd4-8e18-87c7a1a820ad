import torch
import time
import os

def profile_training_step():
    """分析训练步骤的性能瓶颈"""
    
    # 模拟你的训练环境
    os.environ['CUDA_VISIBLE_DEVICES'] = '1,2,3'
    
    # 测试单GPU vs 多GPU的具体差异
    for world_size in [1, 2, 3]:
        print(f"\n=== Testing world_size={world_size} ===")
        
        if world_size == 1:
            device = torch.device('cuda:0')  # 相对GPU 0
        else:
            # 模拟多GPU环境
            device = torch.device('cuda:0')
        
        # 模拟你的模型大小和batch size
        model = torch.nn.Sequential(
            torch.nn.Linear(1000, 512),
            torch.nn.ReLU(),
            torch.nn.Linear(512, 256),
            torch.nn.ReLU(),
            torch.nn.Linear(256, 1)
        ).to(device)
        
        optimizer = torch.optim.Adam(model.parameters(), lr=3e-4)
        
        # 模拟你的batch size
        batch_size = 512 // world_size  # 总batch_size=512
        
        times = []
        for i in range(20):
            torch.cuda.synchronize()
            start_time = time.time()
            
            # 前向传播
            x = torch.randn(batch_size, 1000, device=device)
            y = model(x)
            loss = y.mean()
            
            # 反向传播
            loss.backward()
            
            # 梯度裁剪（你的代码中可能有）
            torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
            
            # 优化器步骤
            optimizer.step()
            optimizer.zero_grad()
            
            torch.cuda.synchronize()
            batch_time = time.time() - start_time
            times.append(batch_time)
            
            if i % 5 == 0:
                print(f"Batch {i}: {batch_time:.3f}s")
        
        avg_time = sum(times) / len(times)
        print(f"World_size {world_size}: Average {avg_time:.3f}s/batch")

if __name__ == "__main__":
    profile_training_step()