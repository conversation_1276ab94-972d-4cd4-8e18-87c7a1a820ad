"""
Online Reinforcement Learning Trainer for Reward Model Training.

This module implements online RL training where:
1. GR1 Policy Model outputs actions
2. CALVIN environment executes actions and provides rewards
3. Reward Model learns to score (obs, action) pairs based on trajectory success
4. Policy can be optionally updated based on reward model feedback

Key features:
- Dual model architecture: Policy (GR1) + Reward Model (existing RM)
- Action noise injection for exploration and new strategy discovery
- Trajectory-level reward assignment based on task completion
- Strict mode implementation with no fallback mechanisms
- Integration with existing reward models and preprocessing pipeline
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass
from pathlib import Path
import time
import logging

# Import existing models
from src.models.gr1_reward_model import GR1RewardModel  
from src.models.reward_model import RobotQNetwork
from src.environments.calvin_wrapper import CalvinRLWrapper, create_calvin_env
from src.utils.preprocessing import preprocess_batch_for_training


@dataclass
class OnlineRLConfig:
    """Configuration for Online RL Training."""

    # Policy configuration
    policy_model_path: str
    policy_config_path: Optional[str] = None  # Path to GR1 config JSON file
    policy_frozen: bool = True
    policy_update_frequency: int = 10
    
    # Reward model configuration
    reward_model_type: str = "gr1"  # "gr1" or "robot_q"
    reward_model_path: Optional[str] = None
    reward_update_frequency: int = 1
    
    # Training configuration
    num_episodes: int = 1000
    max_episode_length: int = 100
    batch_size: int = 32
    learning_rate: float = 1e-4
    
    # Exploration configuration
    action_noise_scale: float = 0.1
    noise_decay_rate: float = 0.995
    min_noise_scale: float = 0.01
    
    # Reward configuration
    reward_success: float = 10.0
    reward_failure: float = -1.0
    reward_step: float = -0.01
    
    # Environment configuration
    task_name: str = "move_slider_left"
    num_parallel_envs: int = 1
    use_language: bool = True
    
    # Training stability
    gradient_clip_norm: float = 1.0
    target_network_update_freq: int = 100
    
    # Logging
    log_frequency: int = 10
    save_frequency: int = 100
    
    # Device
    device: str = "cuda" if torch.cuda.is_available() else "cpu"


class TrajectoryBuffer:
    """Buffer for storing and managing trajectory data."""
    
    def __init__(self, max_size: int = 10000):
        self.max_size = max_size
        self.trajectories = []
        self.current_trajectory = []
        
    def start_trajectory(self):
        """Start a new trajectory."""
        self.current_trajectory = []
        
    def add_step(self, obs: Dict, action: np.ndarray, reward: float, done: bool, info: Dict):
        """Add a step to current trajectory."""
        step_data = {
            'obs': obs,
            'action': action,
            'reward': reward,
            'done': done,
            'info': info
        }
        self.current_trajectory.append(step_data)
        
    def finish_trajectory(self, success: bool):
        """Finish current trajectory and store it."""
        if len(self.current_trajectory) > 0:
            trajectory = {
                'steps': self.current_trajectory.copy(),
                'success': success,
                'length': len(self.current_trajectory),
                'total_reward': sum(step['reward'] for step in self.current_trajectory)
            }
            
            self.trajectories.append(trajectory)
            
            # Maintain buffer size
            if len(self.trajectories) > self.max_size:
                self.trajectories.pop(0)
        
        self.current_trajectory = []
        
    def get_recent_trajectories(self, n: int = 100) -> List[Dict]:
        """Get n most recent trajectories."""
        return self.trajectories[-n:]
        
    def get_successful_trajectories(self) -> List[Dict]:
        """Get all successful trajectories."""
        return [traj for traj in self.trajectories if traj['success']]
        
    def get_failed_trajectories(self) -> List[Dict]:
        """Get all failed trajectories."""
        return [traj for traj in self.trajectories if not traj['success']]


class OnlineRLTrainer:
    """
    Online RL Trainer for Reward Model Training.
    
    Implements the core training loop where:
    1. Policy model (GR1) generates actions
    2. Environment executes actions with optional noise
    3. Reward model learns from trajectory success/failure
    4. Policy optionally updated based on reward model feedback
    """
    
    def __init__(self, config: OnlineRLConfig):
        self.config = config
        self.device = torch.device(config.device)
        
        # Initialize logging
        self._setup_logging()
        
        # Initialize models
        self._load_policy_model()
        self._load_reward_model()
        
        # Initialize environment
        self._create_environment()
        
        # Initialize training components
        self._setup_optimizers()
        self._setup_trajectory_buffer()
        
        # Training state
        self.episode_count = 0
        self.step_count = 0
        self.current_noise_scale = config.action_noise_scale
        
        self.logger.info("✅ OnlineRLTrainer initialized successfully")
        self.logger.info(f"  - Policy frozen: {config.policy_frozen}")
        self.logger.info(f"  - Reward model type: {config.reward_model_type}")
        self.logger.info(f"  - Task: {config.task_name}")
        self.logger.info(f"  - Device: {self.device}")
    
    def _setup_logging(self):
        """Setup logging for training."""
        self.logger = logging.getLogger("OnlineRLTrainer")
        self.logger.setLevel(logging.INFO)
        
        # Prevent duplicate handlers
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
    
    def _load_policy_model(self):
        """Load GR1 policy model for action prediction."""
        self.logger.info(f"Loading policy model from: {self.config.policy_model_path}")

        # Load checkpoint - fail-fast if missing
        checkpoint = torch.load(self.config.policy_model_path, map_location=self.device)

        # Load configuration - following GR-1/evaluate_calvin.py pattern
        if self.config.policy_config_path:
            # Load from external JSON config file (preferred method)
            import json
            self.logger.info(f"Loading GR1 config from: {self.config.policy_config_path}")
            with open(self.config.policy_config_path, 'r') as f:
                variant = json.load(f)
        elif 'config' in checkpoint:
            # Fallback: load from checkpoint if available
            variant = checkpoint['config']
        else:
            # If no config in checkpoint, use actual GR1 configuration
            # Based on GR-1/logs/configs.json - the real configuration used in training
            variant = {
                'state_dim': 7,
                'act_dim': 7,
                'embed_dim': 384,
                'seq_len': 10,
                'img_feat_dim': 768,
                'lang_feat_dim': 512,
                'patch_feat_dim': 768,
                'clip_backbone': 'ViT-B/32',
                'mae_ckpt': '/mnt/disk2/surui/BoostingVLA/GR-1/logs/mae_pretrain_vit_base.pth',
                'resampler_depth': 3,        # Real GR1 config: 3, not 6
                'resampler_dim_head': 128,   # Real GR1 config: 128, not 64
                'resampler_heads': 4,        # Real GR1 config: 4, not 8
                'resampler_num_latents': 9,  # Real GR1 config: 9, not 64
                'resampler_num_media_embeds': 1,  # Real GR1 config: 1, not 256
                'without_norm_pix_loss': False,
                'use_hand_rgb': True,
                'n_layer': 12,
                'n_head': 12,
                'activation_function': 'relu',
                'n_positions': 1024,
                'dropout': 0.1
            }
        
        # Load MAE model
        import sys
        import os
        gr1_path = "/mnt/disk2/surui/BoostingVLA/GR-1"
        if gr1_path not in sys.path:
            sys.path.insert(0, gr1_path)
        import models.vision_transformer as vits
        from models.gr1 import GR1
        import clip
        
        model_mae = vits.__dict__['vit_base'](patch_size=16, num_classes=0)
        model_mae.to(self.device)

        # Load MAE weights - fail-fast if missing
        assert 'mae_ckpt' in variant, "MAE checkpoint path not found in config"
        mae_checkpoint = torch.load(variant['mae_ckpt'], map_location='cpu')
        # Following GR-1/evaluate_calvin.py pattern
        model_mae.load_state_dict(mae_checkpoint['model'], strict=False)
        
        # Load CLIP model
        assert 'clip_backbone' in variant, "CLIP backbone not found in config"
        model_clip, _ = clip.load(variant['clip_backbone'], device=self.device)
        
        # Resampler parameters
        resampler_params = {
            'depth': variant['resampler_depth'],
            'dim_head': variant['resampler_dim_head'], 
            'heads': variant['resampler_heads'],
            'num_latents': variant['resampler_num_latents'],
            'num_media_embeds': variant['resampler_num_media_embeds']
        }
        
        # Create GR1 policy model for action prediction
        self.policy_model = GR1(
            model_clip=model_clip,
            model_mae=model_mae,
            state_dim=variant['state_dim'],
            act_dim=variant['act_dim'],
            hidden_size=variant['embed_dim'],
            sequence_length=variant['seq_len'],
            training_target=['act_pred'],  # Key: enable action prediction
            img_feat_dim=variant['img_feat_dim'],
            lang_feat_dim=variant['lang_feat_dim'],
            patch_feat_dim=variant['patch_feat_dim'],
            resampler_params=resampler_params,
            without_norm_pix_loss=variant['without_norm_pix_loss'],
            use_hand_rgb=variant['use_hand_rgb'],
            n_layer=variant['n_layer'],
            n_head=variant['n_head'],
            n_inner=4*variant['embed_dim'],
            activation_function=variant['activation_function'],
            n_positions=variant['n_positions'],
            resid_pdrop=variant['dropout'],
            attn_pdrop=variant['dropout']
        )
        
        # Load pretrained weights - following GR-1/evaluate_calvin.py pattern
        if 'state_dict' in checkpoint:
            state_dict = checkpoint['state_dict']
        elif 'model' in checkpoint:
            state_dict = checkpoint['model']
        else:
            state_dict = checkpoint

        # Handle DDP module prefix removal if present
        if any(key.startswith('module.') for key in state_dict.keys()):
            self.logger.info("🔧 Removing 'module.' prefix from DDP checkpoint...")
            new_state_dict = {}
            for key, value in state_dict.items():
                if key.startswith('module.'):
                    new_key = key[7:]  # Remove 'module.' prefix (7 characters)
                    new_state_dict[new_key] = value
                else:
                    new_state_dict[key] = value
            state_dict = new_state_dict
            self.logger.info("✅ DDP prefix removal successful")

        self.policy_model.load_state_dict(state_dict, strict=False)
        self.policy_model.to(self.device)
        
        # Freeze policy if configured
        if self.config.policy_frozen:
            for param in self.policy_model.parameters():
                param.requires_grad = False
            self.policy_model.eval()
            self.logger.info("❄️ Policy model frozen")
        else:
            self.policy_model.train()
            self.logger.info("🔥 Policy model trainable")
    
    def _load_reward_model(self):
        """Load reward model for training following validate_models.py pattern."""
        if self.config.reward_model_type == "gr1":
            # Following GR-1/evaluate_calvin.py pattern for GR1RewardModel initialization
            self.reward_model = GR1RewardModel(
                state_dim=7,
                act_dim=7,
                hidden_size=384,
                sequence_length=10,
                img_feat_dim=768,
                patch_feat_dim=768,
                lang_feat_dim=512,
                clip_backbone="ViT-B/32",
                mae_ckpt="/mnt/disk2/surui/BoostingVLA/GR-1/logs/mae_pretrain_vit_base.pth",
                use_hand_rgb=True,
                n_layer=12,
                n_head=12,
                activation_function='relu',
                n_positions=1024,
                dropout=0.1,
                device=str(self.device)
            )

            # Load checkpoint if path provided - following GR-1/evaluate_calvin.py pattern
            if self.config.reward_model_path:
                self.logger.info(f"Loading GR1 reward model from: {self.config.reward_model_path}")
                checkpoint = torch.load(self.config.reward_model_path, map_location=self.device)

                # Handle different checkpoint formats
                if 'model' in checkpoint:
                    state_dict = checkpoint['model']
                elif 'state_dict' in checkpoint:
                    state_dict = checkpoint['state_dict']
                else:
                    state_dict = checkpoint

                # Handle DDP module prefix removal if present
                if any(key.startswith('module.') for key in state_dict.keys()):
                    self.logger.info("🔧 Removing 'module.' prefix from DDP checkpoint...")
                    new_state_dict = {}
                    for key, value in state_dict.items():
                        if key.startswith('module.'):
                            new_key = key[7:]  # Remove 'module.' prefix (7 characters)
                            new_state_dict[new_key] = value
                        else:
                            new_state_dict[key] = value
                    state_dict = new_state_dict
                    self.logger.info("✅ DDP prefix removal successful")

                self.reward_model.load_state_dict(state_dict, strict=False)

        elif self.config.reward_model_type == "robot_q":
            self.reward_model = RobotQNetwork(
                action_dim=7,
                language_dim=384,
                state_dim=7,
                hidden_size=512,
                enable_monitoring=False
            )
            # For robot_q, manually load if path provided
            if self.config.reward_model_path:
                self.logger.info(f"Loading robot_q model from: {self.config.reward_model_path}")
                checkpoint = torch.load(self.config.reward_model_path, map_location=self.device)

                # Handle different checkpoint formats
                if 'state_dict' in checkpoint:
                    state_dict = checkpoint['state_dict']
                elif 'model' in checkpoint:
                    state_dict = checkpoint['model']
                else:
                    state_dict = checkpoint

                # Handle DDP module prefix removal if present
                if any(key.startswith('module.') for key in state_dict.keys()):
                    self.logger.info("🔧 Removing 'module.' prefix from DDP checkpoint...")
                    new_state_dict = {}
                    for key, value in state_dict.items():
                        if key.startswith('module.'):
                            new_key = key[7:]  # Remove 'module.' prefix (7 characters)
                            new_state_dict[new_key] = value
                        else:
                            new_state_dict[key] = value
                    state_dict = new_state_dict
                    self.logger.info("✅ DDP prefix removal successful")

                self.reward_model.load_state_dict(state_dict, strict=False)
        else:
            assert False, f"Unknown reward model type: {self.config.reward_model_type}"
        
        self.reward_model.to(self.device)
        self.reward_model.train()
        
        self.logger.info(f"✅ Reward model loaded: {self.config.reward_model_type}")
    
    def _create_environment(self):
        """Create CALVIN environment for training."""
        self.env = create_calvin_env(
            task_name=self.config.task_name,
            max_episode_length=self.config.max_episode_length,
            use_egl=False,
            show_gui=False,
            reward_success=self.config.reward_success,
            reward_failure=self.config.reward_failure,
            use_language=self.config.use_language
        )
        
        self.logger.info(f"✅ Environment created: {self.config.task_name}")
    
    def _setup_optimizers(self):
        """Setup optimizers for model training."""
        # Reward model optimizer
        self.reward_optimizer = torch.optim.AdamW(
            self.reward_model.parameters(),
            lr=self.config.learning_rate,
            weight_decay=1e-4
        )
        
        # Policy optimizer (if not frozen)
        if not self.config.policy_frozen:
            self.policy_optimizer = torch.optim.AdamW(
                self.policy_model.parameters(),
                lr=self.config.learning_rate * 0.1,  # Lower LR for policy
                weight_decay=1e-4
            )
    
    def _setup_trajectory_buffer(self):
        """Setup trajectory buffer for experience storage."""
        self.trajectory_buffer = TrajectoryBuffer(max_size=1000)
    
    def predict_action(self, obs: Dict[str, Any], add_noise: bool = True) -> np.ndarray:
        """
        Predict action using policy model.
        
        Args:
            obs: Environment observation
            add_noise: Whether to add exploration noise
            
        Returns:
            action: Predicted action array
        """
        self.policy_model.eval()
        
        with torch.no_grad():
            # Convert observation to model input format
            model_input = self._prepare_model_input(obs)
            
            # Get action from GR1 policy model
            prediction = self.policy_model(**model_input)
            
            # Extract arm and gripper actions from GR1 output
            assert 'arm_action_preds' in prediction, "Arm action predictions not found in GR1 output"
            assert 'gripper_action_preds' in prediction, "Gripper action predictions not found in GR1 output"
            
            arm_action_preds = prediction['arm_action_preds']  # [1, seq_len, 6]
            gripper_action_preds = prediction['gripper_action_preds']  # [1, seq_len, 1]
            
            # Use the last timestep's prediction
            arm_action = arm_action_preds[0, -1, :]  # [6]
            gripper_action = gripper_action_preds[0, -1, :]  # [1]
            
            # Apply sigmoid to gripper and convert to binary
            gripper_action = torch.sigmoid(gripper_action)
            gripper_action = (gripper_action > 0.5).float()
            gripper_action = gripper_action * 2.0 - 1.0  # Convert to [-1, 1]
            
            # Combine arm and gripper actions
            action = torch.cat([arm_action, gripper_action], dim=0)  # [7]
            action = action.cpu().numpy()
        
        # Add exploration noise
        if add_noise and self.current_noise_scale > 0:
            noise = np.random.normal(0, self.current_noise_scale, action.shape)
            action = action + noise
            action = np.clip(action, -1.0, 1.0)
        
        return action.astype(np.float32)
    
    def _prepare_model_input(self, obs: Dict[str, Any]) -> Dict[str, torch.Tensor]:
        """Prepare observation for GR1 model input."""
        import clip
        
        # Convert numpy arrays to tensors and normalize
        rgb_static = torch.from_numpy(obs['rgb_static']).float() / 255.0
        rgb_gripper = torch.from_numpy(obs['rgb_gripper']).float() / 255.0
        robot_state = torch.from_numpy(obs['robot_state']).float()
        
        # Add batch and sequence dimensions for GR1
        rgb_static = rgb_static.unsqueeze(0).unsqueeze(0).to(self.device)  # [1, 1, 3, H, W]
        rgb_gripper = rgb_gripper.unsqueeze(0).unsqueeze(0).to(self.device)  # [1, 1, 3, H, W]
        
        # Prepare robot state dict for GR1
        arm_state = robot_state[:6].unsqueeze(0).unsqueeze(0).to(self.device)  # [1, 1, 6]
        gripper_state = robot_state[6:7].unsqueeze(0).unsqueeze(0).to(self.device)  # [1, 1, 1]
        
        # Convert gripper to one-hot encoding
        gripper_binary = (gripper_state > 0).float()  # Convert to binary
        gripper_one_hot = torch.zeros(1, 1, 2).to(self.device)
        gripper_one_hot[0, 0, gripper_binary.long().item()] = 1.0
        
        state_dict = {
            'arm': arm_state,
            'gripper': gripper_one_hot
        }
        
        # Process language instruction
        if 'language_instruction' in obs and obs['language_instruction']:
            language_text = obs['language_instruction']
            language = clip.tokenize([language_text]).to(self.device)
        else:
            # Default instruction for task
            language = clip.tokenize(["perform the task"]).to(self.device)
        
        # Attention mask (1 for valid timesteps)
        attention_mask = torch.ones(1, 1, dtype=torch.long).to(self.device)
        
        return {
            'rgb': rgb_static,
            'hand_rgb': rgb_gripper,
            'state': state_dict,
            'language': language,
            'attention_mask': attention_mask
        }
    
    def evaluate_trajectory(self, trajectory: List[Dict]) -> float:
        """
        Evaluate trajectory using reward model.
        
        Args:
            trajectory: List of trajectory steps
            
        Returns:
            score: Trajectory score from reward model
        """
        self.reward_model.eval()
        
        total_score = 0.0
        
        with torch.no_grad():
            for step in trajectory:
                obs = step['obs']
                action = step['action']
                
                # Prepare input for reward model
                model_input = self._prepare_model_input(obs)
                model_input['action'] = torch.from_numpy(action).float().unsqueeze(0).unsqueeze(1).to(self.device)
                
                # Get reward prediction
                if self.config.reward_model_type == "gr1":
                    output = self.reward_model(**model_input)
                    # STRICT MODE: Direct access without fallback
                    assert 'reward_pred' in output or 'cost_pred' in output, "Neither reward_pred nor cost_pred found in GR1 output"
                    if 'reward_pred' in output:
                        reward_pred = output['reward_pred']
                    else:
                        reward_pred = output['cost_pred']
                else:  # robot_q
                    # Adapt for RobotQNetwork format
                    obs_dict = {
                        'rgb_static': model_input['rgb'].squeeze(1),
                        'rgb_gripper': model_input['hand_rgb'].squeeze(1)
                    }
                    reward_pred = self.reward_model(
                        obs_dict,
                        model_input['action'].squeeze(1),
                        torch.zeros(1, 384).to(self.device),  # Language embedding placeholder
                        model_input['state']['arm'].squeeze(1)
                    )
                
                total_score += reward_pred.item()
        
        return total_score / len(trajectory)
    
    def train_reward_model(self, trajectories: List[Dict]):
        """
        Train reward model on trajectory data.
        
        Args:
            trajectories: List of trajectory data with success labels
        """
        if len(trajectories) < 2:
            return
        
        self.reward_model.train()
        
        # Separate successful and failed trajectories
        successful_trajs = [t for t in trajectories if t['success']]
        failed_trajs = [t for t in trajectories if not t['success']]
        
        if len(successful_trajs) == 0 or len(failed_trajs) == 0:
            return
        
        # Sample trajectories for training
        n_samples = min(self.config.batch_size // 2, len(successful_trajs), len(failed_trajs))
        
        pos_trajs = np.random.choice(successful_trajs, n_samples, replace=False)
        neg_trajs = np.random.choice(failed_trajs, n_samples, replace=False)
        
        total_loss = 0.0
        
        for pos_traj, neg_traj in zip(pos_trajs, neg_trajs):
            # Sample random steps from trajectories
            pos_step = np.random.choice(pos_traj['steps'])
            neg_step = np.random.choice(neg_traj['steps'])
            
            # Get reward predictions
            pos_score = self.evaluate_trajectory([pos_step])
            neg_score = self.evaluate_trajectory([neg_step])
            
            # Ranking loss: positive trajectory should have higher reward
            margin = 1.0
            loss = F.relu(neg_score - pos_score + margin)
            total_loss += loss
        
        # Backward pass
        avg_loss = total_loss / len(pos_trajs)
        
        self.reward_optimizer.zero_grad()
        avg_loss.backward()
        
        # Gradient clipping
        torch.nn.utils.clip_grad_norm_(
            self.reward_model.parameters(),
            self.config.gradient_clip_norm
        )
        
        self.reward_optimizer.step()
        
        self.logger.info(f"Reward model loss: {avg_loss.item():.4f}")
    
    def run_episode(self) -> Dict[str, Any]:
        """
        Run a single episode and collect trajectory data.
        
        Returns:
            episode_info: Dict with episode statistics
        """
        # Reset environment
        obs = self.env.reset()
        
        # Start new trajectory
        self.trajectory_buffer.start_trajectory()
        
        episode_reward = 0.0
        episode_length = 0
        done = False
        
        while not done and episode_length < self.config.max_episode_length:
            # Predict action
            action = self.predict_action(obs, add_noise=True)
            
            # Execute action
            next_obs, reward, done, info = self.env.step(action)
            
            # Store step
            self.trajectory_buffer.add_step(obs, action, reward, done, info)
            
            # Update episode stats
            episode_reward += reward
            episode_length += 1
            self.step_count += 1
            
            # Move to next observation
            obs = next_obs
        
        # Finish trajectory
        success = info['success'] if 'success' in info else False
        self.trajectory_buffer.finish_trajectory(success)
        
        # Update noise scale
        self.current_noise_scale *= self.config.noise_decay_rate
        self.current_noise_scale = max(self.current_noise_scale, self.config.min_noise_scale)
        
        episode_info = {
            'episode_reward': episode_reward,
            'episode_length': episode_length,
            'success': success,
            'noise_scale': self.current_noise_scale
        }
        
        return episode_info
    
    def train(self):
        """Main training loop."""
        self.logger.info(f"Starting online RL training for {self.config.num_episodes} episodes...")
        
        for episode in range(self.config.num_episodes):
            # Run episode
            episode_info = self.run_episode()
            self.episode_count += 1
            
            # Train reward model
            if episode % self.config.reward_update_frequency == 0:
                recent_trajectories = self.trajectory_buffer.get_recent_trajectories(100)
                if len(recent_trajectories) > 10:
                    self.train_reward_model(recent_trajectories)
            
            # Logging
            if episode % self.config.log_frequency == 0:
                success_rate = len(self.trajectory_buffer.get_successful_trajectories()) / max(1, len(self.trajectory_buffer.trajectories))
                
                self.logger.info(
                    f"Episode {episode}: "
                    f"Reward={episode_info['episode_reward']:.2f}, "
                    f"Length={episode_info['episode_length']}, "
                    f"Success={episode_info['success']}, "
                    f"Success Rate={success_rate:.3f}, "
                    f"Noise={episode_info['noise_scale']:.3f}"
                )
            
            # Save checkpoint
            if episode % self.config.save_frequency == 0 and episode > 0:
                self.save_checkpoint(episode)
        
        self.logger.info("✅ Training completed!")
    
    def save_checkpoint(self, episode: int):
        """Save training checkpoint."""
        checkpoint = {
            'episode': episode,
            'reward_model_state_dict': self.reward_model.state_dict(),
            'reward_optimizer_state_dict': self.reward_optimizer.state_dict(),
            'config': self.config,
            'trajectories': len(self.trajectory_buffer.trajectories),
            'success_rate': len(self.trajectory_buffer.get_successful_trajectories()) / max(1, len(self.trajectory_buffer.trajectories))
        }
        
        if not self.config.policy_frozen:
            checkpoint['policy_model_state_dict'] = self.policy_model.state_dict()
            checkpoint['policy_optimizer_state_dict'] = self.policy_optimizer.state_dict()
        
        # Save checkpoint
        save_path = f"online_rl_checkpoint_episode_{episode}.pth"
        torch.save(checkpoint, save_path)
        
        self.logger.info(f"💾 Checkpoint saved: {save_path}")


# Factory function for easy trainer creation
def create_online_rl_trainer(
    policy_model_path: str,
    task_name: str = "move_slider_left",
    reward_model_type: str = "gr1",
    policy_frozen: bool = True,
    num_episodes: int = 1000,
    **kwargs
) -> OnlineRLTrainer:
    """
    Factory function to create OnlineRLTrainer.
    
    Args:
        policy_model_path: Path to pretrained policy model
        task_name: CALVIN task name
        reward_model_type: Type of reward model ("gr1" or "robot_q")
        policy_frozen: Whether to freeze policy model
        num_episodes: Number of training episodes
        **kwargs: Additional config parameters
        
    Returns:
        trainer: Configured OnlineRLTrainer instance
    """
    config = OnlineRLConfig(
        policy_model_path=policy_model_path,
        task_name=task_name,
        reward_model_type=reward_model_type,
        policy_frozen=policy_frozen,
        num_episodes=num_episodes,
        **kwargs
    )
    
    return OnlineRLTrainer(config)