"""
Base Configuration for All Trainers

This module provides the base configuration class that contains common parameters
shared across all training methods (IQL, CQL, IRL, etc.).
"""

from dataclasses import dataclass
from typing import Optional


@dataclass
class BaseConfig:
    """
    Base configuration class containing common parameters for all trainers.
    
    All specific trainer configs (IQLConfig, CQLConfig, IRLConfig) should inherit from this.
    """
    
    # Basic training parameters
    device: str = 'cuda'
    batch_size: int = 256
    
    # Learning rate scheduler parameters
    use_warmup: bool = True
    warmup_epochs: int = 3                    # Number of epochs for linear warmup
    warmup_start_lr: float = 1e-6             # Starting learning rate for warmup
    use_cosine_annealing: bool = True         # Whether to use cosine annealing after warmup
    cosine_annealing_eta_min: float = 1e-6   # Minimum learning rate for cosine annealing
    
    # Optional total epochs for proper cosine annealing scheduling
    total_epochs: Optional[int] = 30          # Total training epochs (for scheduler)
    
    # 统一损失函数权重配置
    gcl_weight: float = 0.0                   # GCL损失权重
    preference_weight: float = 1.0            # Bradley-Terry偏好损失权重
    rtg_weight: float = 0.0                   # RTG expectile损失权重
    
    # 损失函数通用配置
    loss_function: str = "bradley_terry"      # 偏好损失类型
    loss_margin_weight: float = 0.1           # Bradley-<PERSON>边际权重
    loss_cost_threshold: float = 0.3          # 成本阈值
    prob_epsilon: float = 1e-7                # GCL数值稳定性参数
    exp_clamp_max: float = 10.0               # 指数裁剪最大值
    rtg_tau: float = 0.9                      # RTG expectile参数
    rtg_loss_type: str = "rtg_expectile"      # RTG损失类型