"""
Bradley-<PERSON> Loss Function
基于RoboMonkey论文的稳定偏好学习损失函数
"""

import torch
import torch.nn.functional as F
import numpy as np
from typing import Dict, Any
from .base_loss import BaseLoss, LossOutput

class BradleyTerryLoss(BaseLoss):
    """
    Bradley-<PERSON>偏好学习损失函数
    
    基于RoboMonkey论文的方法，使用sigmoid函数确保数值稳定性
    L = -E[log σ(R(a_W) - R(a_L) - α ||Δ* - Δ̂||²)]
    
    其中：
    - a_W, a_L 是偏好对（胜者/败者动作）
    - Δ* 是真实偏好强度差异
    - Δ̂ 是预测偏好强度差异  
    - α 是边际权重
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        
        # Bradley-Terry特定参数
        self.margin_weight = config.get('margin_weight', 0.1)
        # 移除 preference_pairs_per_sample - 现在固定构造1个偏好对（最好vs最坏）
        self.reward_threshold_percentile = config.get('cost_threshold_percentile', 0.3)  # 保留以向后兼容
        
    def compute_loss(self, **kwargs) -> LossOutput:
        """计算Bradley-Terry偏好学习损失（严格按照RoboMonkey论文实现）
        
        RoboMonkey论文方法：
        1. 选择1个最好的样本（最小RMSE）作为 a_W
        2. 选择1个最坏的样本（最大RMSE）作为 a_L  
        3. 构造1个偏好对: (a_W, a_L)
        4. 计算损失: L = -log σ(R(a_W) - R(a_L) - α||Δ* - Δ̂||²)
        
        关键公式：
        - Δ* = |RMSE(a_W, a*) - RMSE(a_L, a*)|  (真实偏好强度)
        - Δ̂ = |R(a_W) - R(a_L)|                (预测偏好强度)
        """
        # 提取参数
        sample_rewards = kwargs['sample_rewards']  # [B*N] - 所有采样动作的reward
        batch_data = kwargs['batch_data']
        rmse_distances = batch_data['rmse_distances']  # [B*N] - 与expert action的RMSE距离
        batch_size = batch_data['batch_size']
        
        num_samples_per_expert = sample_rewards.shape[0] // batch_size
        
        # 重新整形为 [B, N]
        sample_rewards_reshaped = sample_rewards.view(batch_size, num_samples_per_expert)
        rmse_distances_reshaped = rmse_distances.view(batch_size, num_samples_per_expert)
        
        # ===== 按照RoboMonkey论文：构造真正的偏好对 =====
        
        # 1. 选择最好的1个样本（最小RMSE）作为 a_W
        _, best_indices = torch.topk(rmse_distances_reshaped, 1, dim=1, largest=False)  # [B, 1]
        
        # 2. 选择最坏的1个样本（最大RMSE）作为 a_L
        _, worst_indices = torch.topk(rmse_distances_reshaped, 1, dim=1, largest=True)  # [B, 1]
        
        # 3. 获取偏好对的信息
        batch_indices = torch.arange(batch_size, device=sample_rewards.device).unsqueeze(1)  # [B, 1]
        
        # Reward values - 每个batch一个偏好对
        good_reward = sample_rewards_reshaped[batch_indices, best_indices].squeeze(1)   # [B] - R(a_W)
        bad_reward = sample_rewards_reshaped[batch_indices, worst_indices].squeeze(1)   # [B] - R(a_L)
        
        # RMSE distances - 真实的偏好信息
        good_rmse = rmse_distances_reshaped[batch_indices, best_indices].squeeze(1)     # [B] - RMSE(a_W, a*)
        bad_rmse = rmse_distances_reshaped[batch_indices, worst_indices].squeeze(1)     # [B] - RMSE(a_L, a*)
        
        # 4. 计算真实偏好强度差异 Δ* (论文公式)
        # Δ* = |RMSE(a_W, a*) - RMSE(a_L, a*)|
        delta_star = torch.abs(good_rmse - bad_rmse)  # [B] - 每个batch一个值
        
        # 5. 计算预测偏好强度差异 Δ̂ (论文公式)
        # Δ̂ = |R(a_W) - R(a_L)|
        delta_hat = torch.abs(good_reward - bad_reward)  # [B] - 每个batch一个值
        
        # 6. 边际项 (论文公式)
        # α ||Δ* - Δ̂||²
        margin_term = self.margin_weight * torch.square(delta_star - delta_hat)  # [B]
        
        # 7. Bradley-Terry损失 (论文公式)
        # L = -log σ(R(a_W) - R(a_L) - α||Δ* - Δ̂||²)
        # 好动作应该有更高的reward（因为RMSE越小越好）
        reward_difference = good_reward - bad_reward  # [B]
        logits = reward_difference - margin_term  # [B]
        pair_losses = -torch.log(torch.sigmoid(logits) + 1e-8)  # [B]
        
        # 8. 计算最终损失（所有batch的平均）
        final_loss = pair_losses.mean()  # 标量
        
        # 计算详细指标 - 返回tensor格式，由上层决定何时同步
        metrics = {
            'bt_loss': final_loss.detach(),
            'good_reward_mean': good_reward.mean(),        # 好动作的平均reward
            'bad_reward_mean': bad_reward.mean(),          # 坏动作的平均reward
            'good_rmse_mean': good_rmse.mean(),            # 好动作的平均RMSE（应该较小）
            'bad_rmse_mean': bad_rmse.mean(),              # 坏动作的平均RMSE（应该较大）
            'sample_reward_mean': sample_rewards.mean(),   # 所有样本的平均reward
            'num_preference_pairs': batch_size,           # 偏好对数量 = batch_size
            'preference_strength_real': delta_star.mean(),     # 真实偏好强度 Δ*
            'preference_strength_pred': delta_hat.mean(),      # 预测偏好强度 Δ̂
            'margin_term_mean': margin_term.mean(),            # 边际项大小
            'reward_advantage': good_reward.mean() - bad_reward.mean(),  # 期望>0
            'rmse_advantage': bad_rmse.mean() - good_rmse.mean(),        # 期望>0
        }

        return LossOutput(loss=final_loss, metrics=metrics)