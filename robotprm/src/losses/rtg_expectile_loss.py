"""
RTG Expectile Regression Loss Function
基于 ReinBoT 论文的 ReturnToGo expectile regression 损失实现
"""

import torch
from typing import Dict, Any
from .base_loss import BaseLoss, LossOutput


class RTGExpectileLoss(BaseLoss):
    """
    RTG Expectile Regression 损失函数
    
    基于 ReinBoT 论文的方法，使用 expectile regression 训练模型预测 ReturnToGo 值
    Loss = E[|τ - I(u < 0)| * u²]
    其中 u = predictions - targets, τ = expectile 参数 (通常为 0.9)
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.tau = config.get('rtg_tau', 0.9)  # expectile 参数
        
        if not 0 < self.tau < 1:
            raise ValueError(f"expectile 参数 tau 必须在 (0,1) 范围内，当前值: {self.tau}")
    
    def asymmetric_l2_loss(self, residuals: torch.Tensor, tau: float) -> torch.Tensor:
        """计算 asymmetric L2 loss (expectile regression)"""
        indicator = (residuals < 0).float()
        weights = torch.abs(tau - indicator)
        weighted_squared_loss = weights * (residuals ** 2)
        return torch.mean(weighted_squared_loss)
    
    def compute_loss(self, **kwargs) -> LossOutput:
        """
        计算 RTG expectile regression 损失

        支持两种参数名：
        - predictions/targets (标准名称)
        - reward_pred/rtg_targets (RTG trainer 使用的名称)
        """
        # 灵活的参数提取
        predictions = kwargs.get('reward_pred')
        targets = kwargs.get('rtg_targets')

        # 计算残差和损失
        residuals = predictions - targets
        expectile_loss = self.asymmetric_l2_loss(residuals, self.tau)

        # 计算监控指标 - 返回tensor格式，由上层决定何时同步
        with torch.no_grad():
            abs_residuals = torch.abs(residuals)
            squared_residuals = residuals ** 2

            metrics = {
                'rtg_expectile_loss': expectile_loss.detach(),
                'mae': abs_residuals.mean(),
                'mse': squared_residuals.mean(),
                'rmse': torch.sqrt(squared_residuals.mean()),
                'predictions_mean': predictions.mean(),
                'targets_mean': targets.mean(),
                'residuals_mean': residuals.mean(),
                'tau': self.tau,
                'batch_size': predictions.shape[0],
            }

        return LossOutput(loss=expectile_loss, metrics=metrics)
