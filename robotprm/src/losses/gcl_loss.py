"""
Guided Cost Learning (GCL) Loss Function
原有的GCL损失实现，保持向后兼容性
"""

import torch
import torch.nn.functional as F
from typing import Dict, Any
from .base_loss import BaseLoss, LossOutput

class GCLLoss(BaseLoss):
    """
    Guided Cost Learning (GCL) 损失函数
    
    原有的IRL损失实现：
    GCL Loss = E[cost_demo] + log(E[exp(-cost_samp) / prob])
    
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        
        # GCL特定参数
        self.prob_epsilon = config.get('prob_epsilon', 1e-7)
        self.exp_clamp_max = config.get('exp_clamp_max', 10.0)  # 限制exp的最大值
        
    def compute_loss(self, **kwargs) -> LossOutput:
        """计算GCL损失，支持与trainers对齐的接口
        
        GCL损失应该计算expert action和一个noisy action之间的对比:
        - expert action应该有低cost（高reward）
        - noisy action应该有高cost（低reward）
        
        现在trainers正确传递了expert_rewards，我们可以正确计算GCL损失
        """
        
        # 从kwargs中提取参数
        expert_rewards = kwargs['expert_rewards']  # [B] - 专家动作的reward
        sample_rewards = kwargs['sample_rewards']  # [B*N] - 所有采样动作的reward
        batch_data = kwargs['batch_data']
        rmse_distances = batch_data['rmse_distances']  # [B*N] - 与expert action的RMSE距离
        batch_size = batch_data['batch_size']
        
        # 计算样本概率（基于RMSE距离）
        prob_scale = 10.0  # 固定概率因子，与trainers对齐
        sample_probs = torch.exp(-prob_scale * rmse_distances).clamp(0.01, 0.9)  # [B*N]
        
        # 重新整形为 [B, N] 
        num_samples_per_expert = sample_rewards.shape[0] // batch_size
        sample_rewards_reshaped = sample_rewards.view(batch_size, num_samples_per_expert)  # [B, N]
        rmse_distances_reshaped = rmse_distances.view(batch_size, num_samples_per_expert)  # [B, N]
        sample_probs_reshaped = sample_probs.view(batch_size, num_samples_per_expert)  # [B, N]
        
        # 从每个batch的N个噪声动作中选择一个（选择最远的，即最差的）
        _, worst_indices = torch.topk(rmse_distances_reshaped, 1, dim=1, largest=True)  # [B, 1]
        batch_indices = torch.arange(batch_size, device=sample_rewards.device).unsqueeze(1)  # [B, 1]
        
        # 选择的噪声动作的reward和概率
        selected_sample_rewards = sample_rewards_reshaped[batch_indices, worst_indices].squeeze(1)  # [B]
        selected_sample_probs = sample_probs_reshaped[batch_indices, worst_indices].squeeze(1)  # [B]
        
        # 将rewards转换为costs（cost = -reward）
        expert_costs = -expert_rewards  # [B] - 专家动作的cost（应该较低）
        sample_costs = -selected_sample_rewards  # [B] - 采样动作的cost（应该较高）
        
        # GCL损失公式: E[cost_demo] + log(E[exp(-cost_samp) / prob])
        # 使用7d99851版本的简洁实现
        cost_demo_mean = torch.mean(expert_costs)  # scalar
        
        # 计算exp(-cost_samp) / prob，添加数值稳定性保护
        negative_costs = -sample_costs  # [B]
        # 限制exp的输入范围以避免数值爆炸
        negative_costs_clamped = torch.clamp(negative_costs, max=self.exp_clamp_max)
        
        exp_negative_cost = torch.exp(negative_costs_clamped)  # [B]
        exp_negative_cost_over_prob = exp_negative_cost / (selected_sample_probs + self.prob_epsilon)  # [B]
        
        # 计算log期望值
        log_expectation = torch.log(torch.mean(exp_negative_cost_over_prob) + self.prob_epsilon)  # scalar
        
        gcl_loss = cost_demo_mean + log_expectation

        # 计算指标用于监控数值稳定性 - 返回tensor格式，由上层决定何时同步
        metrics = {
            'gcl_loss': gcl_loss.detach(),
            'cost_demo_mean': cost_demo_mean,
            'log_expectation': log_expectation,
            'advantage': expert_rewards.mean() - selected_sample_rewards.mean(),
            'expert_reward_mean': expert_rewards.mean(),
            'expert_reward_std': expert_rewards.std(),
            'sample_reward_mean': selected_sample_rewards.mean(),
            'prob_mean': selected_sample_probs.mean(),
            # 数值稳定性监控
            'exp_negative_cost_max': exp_negative_cost.max(),
            'exp_negative_cost_over_prob_max': exp_negative_cost_over_prob.max(),
            'exp_negative_cost_over_prob_mean': exp_negative_cost_over_prob.mean(),
            'negative_costs_clamped_max': negative_costs_clamped.max(),
        }

        return LossOutput(loss=gcl_loss, metrics=metrics)