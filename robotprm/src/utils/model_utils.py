"""
Unified model creation utilities to eliminate code duplication.

This module provides unified functions for creating models, DDP wrappers,
and logging model statistics across all training methods.
"""

import torch
from torch.nn.parallel import DistributedDataParallel as DDP
import logging
from models.reward_model import QNetwork
from models.gr1_reward_model import GR1RewardModel
from trainers.irl_trainer import <PERSON><PERSON><PERSON><PERSON><PERSON>, IRLConfig
from trainers.rtg_trainer import RT<PERSON><PERSON>rainer, RTGConfig

logger = logging.getLogger(__name__)


def create_model(method: str, args, device, action_dim=7, language_dim=384, state_dim=7):
    """
    Create a unified model for any training method.
    
    Args:
        method: Training method ('irl', 'rtg')
        args: Arguments containing hidden_size, num_layers, etc.
        device: Device to place model on
        action_dim: Action dimension
        language_dim: Language embedding dimension  
        state_dim: State dimension
        
    Returns:
        Model instance
    """
    if method == 'irl':
        # IRL method uses QNetwork
        class ModelArgs:
            def __init__(self):
                self.frozen_vision = True
        
        model_args = ModelArgs()
        model = QNetwork(
            action_dim=action_dim,
            language_dim=language_dim, 
            state_dim=state_dim,
            hidden_size=args.hidden_size,
            num_layers=args.num_layers,
            args=model_args
        )
        return model.to(device)
    elif method == 'rtg':
        # RTG method uses GR1 reward model (使用固化的参数)
        model = GR1RewardModel(
            state_dim=state_dim,
            act_dim=action_dim,
            hidden_size=384,  # GR-1固定384
            sequence_length=10,  # GR-1固定10
            img_feat_dim=768,   # GR-1固定768
            patch_feat_dim=768,  # GR-1固定768
            lang_feat_dim=language_dim,
            clip_backbone=args.clip_backbone,
            mae_ckpt=args.mae_ckpt,
            pretrained_gr1_path=args.pretrained_gr1_path,
            use_hand_rgb=args.use_hand_rgb,
            # GR-1额外的GPT参数（固定值）
            n_layer=12,
            n_head=12,
            activation_function='relu',
            n_positions=1024,
            dropout=0.1,
            device=str(device)
        )
        return model.to(device)
    else:
        raise ValueError(f"Unknown method: {method}. Supported methods: 'irl', 'rtg'")


def create_ddp_model(model, rank: int):
    """
    Create DDP wrapped model.
    
    Args:
        model: Model to wrap
        rank: GPU rank
        
    Returns:
        DDP wrapped model
    """
    return DDP(model, device_ids=[rank], find_unused_parameters=True)


def log_model_stats(model, method_name: str, rank: int):
    """
    Log model parameter statistics.
    
    Args:
        model: Model to analyze
        method_name: Name of the method for logging
        rank: GPU rank (only rank 0 logs)
    """
    if rank == 0:
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        frozen_params = total_params - trainable_params
        
        print(f"\033[33m📊 {method_name.upper()} Model Parameters:")
        print(f"\033[33m  Total: {total_params:,}")
        print(f"\033[33m  Trainable: {trainable_params:,}")
        print(f"\033[33m  Frozen: {frozen_params:,}")
        print(f"\033[33m  Trainable %: {trainable_params/total_params*100:.1f}%")
        print("\033[0m")
        torch.cuda.empty_cache()


def create_trainer(method: str, ddp_model, config):
    """
    Create trainer instance for the specified method.
    
    Args:
        method: Training method ('irl', 'rtg')
        ddp_model: DDP wrapped model
        config: Training configuration
        
    Returns:
        Trainer instance
    """
    if method == 'irl':
        return IRLTrainer(ddp_model, config)
    elif method == 'rtg':
        return RTGTrainer(ddp_model, config)
    else:
        raise ValueError(f"Unknown method: {method}. Supported methods: 'irl', 'rtg'")


def create_training_config(method: str, args, train_batch_size: int, device):
    """
    Create training configuration for the specified method.
    
    Args:
        method: Training method ('irl', 'rtg')
        args: Command line arguments
        train_batch_size: Batch size per GPU
        device: Training device
        
    Returns:
        Configuration object
    """
    if method == 'irl':
        config = IRLConfig(
            learning_rate=args.learning_rate_q,
            batch_size=train_batch_size,
            device=device,
            total_epochs=args.total_epochs,
            gcl_weight=args.gcl_weight,
            preference_weight=args.preference_weight,
            rtg_weight=args.rtg_weight
        )
    elif method == 'rtg':
        config = RTGConfig(
            learning_rate=args.learning_rate_q,
            rtg_weight=args.rtg_weight,
            preference_weight=args.preference_weight,
            batch_size=train_batch_size,
            device=device,
            total_epochs=args.total_epochs,
            gcl_weight=args.gcl_weight
        )
    else:
        raise ValueError(f"Unknown method: {method}. Supported methods: 'irl', 'rtg'")
    
    # 使用pprint输出工整格式的config
    from pprint import pformat
    print(f"🔧 训练配置:\n{pformat(vars(config), indent=4)}")
    return config


def log_training_config(method: str, args, rank: int):
    """
    Log training configuration - silent for clean training output.
    
    Args:
        method: Training method
        args: Command line arguments  
        rank: GPU rank (only rank 0 logs)
    """
    # Silent execution - no verbose output during training
    pass