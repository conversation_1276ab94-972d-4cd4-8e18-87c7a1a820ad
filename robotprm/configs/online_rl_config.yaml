# Online RL Training Configuration
# Configuration for training reward models through online reinforcement learning
# with CALVIN simulation environment and GR1 policy model

# ============================================================================
# Policy Model Configuration
# ============================================================================
policy:
  # Path to pretrained GR1 policy model checkpoint
  model_path: "/mnt/disk2/surui/BoostingVLA/GR-1/logs/snapshot_ABC.pt"

  # Path to GR1 model configuration JSON file
  # This should match the configuration used during GR1 training
  # If not provided, will use default configuration (may cause shape mismatches)
  config_path: "/mnt/disk2/surui/BoostingVLA/GR-1/logs/configs.json"

  # Whether to freeze policy model during training
  # true: Policy remains fixed, only reward model learns
  # false: Both policy and reward model are updated
  frozen: true

  # Frequency of policy updates (episodes)
  # Only used if frozen: false
  update_frequency: 10

# ============================================================================
# Reward Model Configuration  
# ============================================================================
reward:
  # Type of reward model to use
  # Options: "gr1" (GR1RewardModel) or "robot_q" (RobotQNetwork)
  type: "gr1"
  
  # Path to pretrained reward model (optional)
  # If null, reward model is initialized randomly
  model_path: null
  
  # Frequency of reward model updates (episodes)
  update_frequency: 1

# ============================================================================
# Training Configuration
# ============================================================================
training:
  # Number of training episodes
  num_episodes: 1000
  
  # Maximum steps per episode
  max_episode_length: 100
  
  # Batch size for reward model training
  batch_size: 32
  
  # Learning rate for optimizers
  learning_rate: 1e-4
  
  # Training device
  device: "cuda"
  
  # Gradient clipping norm
  gradient_clip_norm: 1.0
  
  # Target network update frequency (episodes)
  target_network_update_freq: 100

# ============================================================================
# Exploration Configuration
# ============================================================================
exploration:
  # Exploration strategy type
  # Options: "gaussian", "multimodal", "curriculum"
  strategy_type: "gaussian"
  
  # Initial action noise scale
  initial_noise_scale: 0.1
  
  # Minimum noise scale (noise floor)
  min_noise_scale: 0.01
  
  # Maximum noise scale (noise ceiling)
  max_noise_scale: 0.5
  
  # Noise decay rate per episode
  noise_decay_rate: 0.995
  
  # Success rate threshold to reduce noise (exploitation)
  success_threshold: 0.7
  
  # Success rate threshold to increase noise (exploration)
  failure_threshold: 0.3
  
  # Number of episodes to consider for adaptation
  adaptation_window: 50
  
  # Trajectory diversity threshold for novelty detection
  diversity_threshold: 0.1
  
  # Enable curriculum learning
  enable_curriculum: true

# ============================================================================
# Environment Configuration
# ============================================================================
environment:
  # CALVIN task name
  # Supported tasks: move_slider_left, move_slider_right, open_drawer, 
  #                  close_drawer, lift_red_block_slider, etc.
  task_name: "move_slider_left"
  
  # Path to CALVIN installation
  calvin_path: "/mnt/disk2/surui/BoostingVLA/calvin"
  
  # Number of parallel environments (future extension)
  num_parallel_envs: 1
  
  # Whether to use language instructions
  use_language: true

# ============================================================================
# Reward Configuration
# ============================================================================
rewards:
  # Reward for successful task completion
  success: 10.0
  
  # Reward for task failure
  failure: -1.0
  
  # Small negative reward per step (encourages efficiency)
  step: -0.01

# ============================================================================
# Logging Configuration
# ============================================================================
logging:
  # Episode logging frequency
  frequency: 10
  
  # Checkpoint saving frequency (episodes)
  save_frequency: 100

# ============================================================================
# Example Alternative Configurations
# ============================================================================

# Uncomment sections below for different training scenarios:

# # High exploration configuration
# exploration:
#   strategy_type: "multimodal"
#   initial_noise_scale: 0.2
#   min_noise_scale: 0.05
#   max_noise_scale: 0.8
#   noise_decay_rate: 0.99
#   enable_curriculum: false

# # Curriculum learning configuration
# exploration:
#   strategy_type: "curriculum"
#   initial_noise_scale: 0.3
#   min_noise_scale: 0.01
#   curriculum_stages: 5
#   stage_episode_duration: 200

# # Policy training configuration (unfrozen policy)
# policy:
#   frozen: false
#   update_frequency: 5
# training:
#   learning_rate: 5e-5  # Lower LR for policy updates

# # Long training configuration
# training:
#   num_episodes: 5000
#   max_episode_length: 200
#   batch_size: 64
# logging:
#   frequency: 25
#   save_frequency: 250