# 简化配置系统使用示例

## 新的启动方式

配置系统已经简化，现在可以直接使用 Python 脚本加少量参数启动训练，不再需要复杂的 shell 脚本。

### RTG训练示例

```bash
# 基础RTG训练（使用默认参数）
python scripts/train_unified.py --method rtg

# 自定义RTG训练参数
python scripts/train_unified.py --method rtg \
    --learning_rate_q 5e-5 \
    --batch_size 256 \
    --preference_weight 0.9 \
    --rtg_weight 0.1 \
    --rtg_tau 0.9

# RTG训练使用GCL + RTG双损失
python scripts/train_unified.py --method rtg \
    --gcl_weight 0.5 \
    --rtg_weight 0.5 \
    --preference_weight 0.0

# 长时间训练
python scripts/train_unified.py --method rtg \
    --total_epochs 50 \
    --learning_rate_q 3e-4

# 调试模式
python scripts/train_unified.py --method rtg \
    --debug_only \
    --world_size 1
```

### IRL训练示例

```bash
# 基础IRL训练
python scripts/train_unified.py --method irl

# 自定义IRL训练参数
python scripts/train_unified.py --method irl \
    --learning_rate_q 5e-5 \
    --batch_size 256 \
    --preference_weight 1.0

# IRL训练使用GCL损失
python scripts/train_unified.py --method irl \
    --gcl_weight 1.0 \
    --preference_weight 0.0

# 单GPU IRL训练
python scripts/train_unified.py --method irl \
    --world_size 1 \
    --batch_size 80
```

### 核心可调参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--method` | `rtg` | 训练方法：`irl` 或 `rtg` |
| `--learning_rate_q` | `3e-4` | 学习率 |
| `--batch_size` | `320` | 总批次大小（80 per GPU） |
| `--total_epochs` | `30` | 训练轮数 |
| `--gcl_weight` | `0.0` | GCL loss权重 |
| `--preference_weight` | `1.0` | Preference loss权重 |
| `--rtg_weight` | `0.1` | RTG loss权重（仅RTG方法） |
| `--rtg_tau` | `0.7` | RTG expectile参数（仅RTG方法） |
| `--world_size` | `4` | GPU数量 |

### 固化参数

以下参数已固化，不再需要手动设置：

- **GR1架构参数**：`hidden_size=384`, `num_layers=12`, `sequence_length=10`
- **视觉特征维度**：`img_feat_dim=768`, `patch_feat_dim=768`
- **CLIP配置**：`clip_backbone="ViT-B/32"`
- **RTG特定参数**：`rtg_gamma=0.99`, `rtg_warmup_steps=1000`
- **数据配置**：`use_shared_memory=True`（默认启用）

### 废弃参数

以下参数已移除，不再支持：

- **CQL相关**：`cql_alpha`, `cql_n_actions`, `cql_freq`, `use_double_q`
- **IQL相关**：`learning_rate_v`, `tau`（IQL方法特定）
- **架构参数**：`hidden_size`, `num_layers`（已固化）
- **IRL Policy**：`irl_policy` 方法（已移除）

## 与之前版本的对比

### 之前（复杂的shell脚本）：
```bash
./scripts/run_rtg_train.sh --shm \
    --gpus "0,1,2,3" \
    --world_size 4 \
    --per_gpu_bs 80 \
    --epochs 30 \
    --hidden_size 384 \
    --num_layers 12 \
    --sequence_length 10 \
    --learning_rate 3e-4 \
    --rtg_weight 0.1 \
    --preference_weight 1.0 \
    --rtg_tau 0.7 \
    --rtg_gamma 0.99 \
    --rtg_warmup_steps 1000 \
    --img_feat_dim 768 \
    --patch_feat_dim 768 \
    --clip_backbone "ViT-B/32" \
    --use_hand_rgb
```

### 现在（简化的Python命令）：
```bash
python scripts/train_unified.py --method rtg \
    --learning_rate_q 3e-4 \
    --preference_weight 1.0 \
    --rtg_weight 0.1
```

## 常见使用场景

### 1. 快速实验
```bash
# 最简单的RTG训练
python scripts/train_unified.py --method rtg

# 快速调试
python scripts/train_unified.py --method rtg --debug_only
```

### 2. 参数调优
```bash
# 调整学习率
python scripts/train_unified.py --method rtg --learning_rate_q 5e-5

# 调整损失权重
python scripts/train_unified.py --method rtg --preference_weight 0.8 --rtg_weight 0.2

# 使用GCL损失
python scripts/train_unified.py --method irl --gcl_weight 1.0 --preference_weight 0.0
```

### 3. 资源受限环境
```bash
# 单GPU训练
python scripts/train_unified.py --method rtg --world_size 1 --batch_size 80

# 小批次训练
python scripts/train_unified.py --method rtg --batch_size 160
```

### 4. 长时间训练
```bash
# 长时间训练
python scripts/train_unified.py --method rtg --total_epochs 100 --learning_rate_q 2e-4
```

这样的简化大大降低了使用门槛，现在只需要关注真正重要的超参数。