#!/usr/bin/env python3
"""
性能修复验证脚本

测试RTG trainer和损失函数的性能优化效果：
1. 验证.item()调用减少
2. 测试GPU同步频率控制
3. 确保训练逻辑正确性
"""

import torch
import time
import numpy as np
from typing import Dict, Any

# 模拟RTG trainer配置
class MockRTGConfig:
    def __init__(self):
        self.detailed_log_frequency = 10  # 每10步记录详细统计
        self.gcl_weight = 0.9
        self.preference_weight = 0.9
        self.rtg_weight = 0.1
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'

def test_loss_function_performance():
    """测试损失函数的性能优化效果"""
    print("🔧 测试损失函数性能优化...")
    
    # 导入损失函数
    import sys
    sys.path.append('src')
    from losses.rtg_expectile_loss import RTGExpectileLoss
    from losses.bradley_terry_loss import BradleyTerryLoss
    from losses.gcl_loss import GCLLoss
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    batch_size = 32
    
    # 创建测试数据
    predictions = torch.randn(batch_size, device=device)
    targets = torch.randn(batch_size, device=device)
    sample_rewards = torch.randn(batch_size * 2, device=device)
    expert_rewards = torch.randn(batch_size, device=device)
    rmse_distances = torch.rand(batch_size * 2, device=device)
    
    batch_data = {
        'batch_size': batch_size,
        'rmse_distances': rmse_distances
    }
    
    # 测试RTG Expectile Loss
    rtg_loss = RTGExpectileLoss({'rtg_tau': 0.9})
    
    print("📊 RTG Expectile Loss 测试:")
    
    # 测试同步模式
    start_time = time.time()
    for _ in range(10):
        output_sync = rtg_loss.compute_loss(
            reward_pred=predictions,
            rtg_targets=targets,
            sync_stats=True
        )
    sync_time = time.time() - start_time
    print(f"  同步模式 (10次): {sync_time:.4f}s")
    print(f"  统计类型: {type(output_sync.metrics['mae'])}")
    
    # 测试非同步模式
    start_time = time.time()
    for _ in range(10):
        output_async = rtg_loss.compute_loss(
            reward_pred=predictions,
            rtg_targets=targets,
            sync_stats=False
        )
    async_time = time.time() - start_time
    print(f"  非同步模式 (10次): {async_time:.4f}s")
    print(f"  统计类型: {type(output_async.metrics['mae'])}")
    print(f"  性能提升: {((sync_time - async_time) / sync_time * 100):.1f}%")
    
    # 测试Bradley Terry Loss
    bt_loss = BradleyTerryLoss({'margin_weight': 0.1})
    
    print("\n📊 Bradley Terry Loss 测试:")
    
    start_time = time.time()
    for _ in range(10):
        bt_output_sync = bt_loss.compute_loss(
            sample_rewards=sample_rewards,
            batch_data=batch_data,
            sync_stats=True
        )
    bt_sync_time = time.time() - start_time
    print(f"  同步模式 (10次): {bt_sync_time:.4f}s")
    
    start_time = time.time()
    for _ in range(10):
        bt_output_async = bt_loss.compute_loss(
            sample_rewards=sample_rewards,
            batch_data=batch_data,
            sync_stats=False
        )
    bt_async_time = time.time() - start_time
    print(f"  非同步模式 (10次): {bt_async_time:.4f}s")
    print(f"  性能提升: {((bt_sync_time - bt_async_time) / bt_sync_time * 100):.1f}%")
    
    # 测试GCL Loss
    gcl_loss = GCLLoss({'prob_epsilon': 1e-7, 'exp_clamp_max': 10.0})
    
    print("\n📊 GCL Loss 测试:")
    
    start_time = time.time()
    for _ in range(10):
        gcl_output_sync = gcl_loss.compute_loss(
            expert_rewards=expert_rewards,
            sample_rewards=sample_rewards,
            batch_data=batch_data,
            sync_stats=True
        )
    gcl_sync_time = time.time() - start_time
    print(f"  同步模式 (10次): {gcl_sync_time:.4f}s")
    
    start_time = time.time()
    for _ in range(10):
        gcl_output_async = gcl_loss.compute_loss(
            expert_rewards=expert_rewards,
            sample_rewards=sample_rewards,
            batch_data=batch_data,
            sync_stats=False
        )
    gcl_async_time = time.time() - start_time
    print(f"  非同步模式 (10次): {gcl_async_time:.4f}s")
    print(f"  性能提升: {((gcl_sync_time - gcl_async_time) / gcl_sync_time * 100):.1f}%")

def test_trainer_sync_frequency():
    """测试训练器的同步频率控制"""
    print("\n🔧 测试训练器同步频率控制...")
    
    config = MockRTGConfig()
    
    # 模拟训练步骤
    total_loss = torch.tensor(1.5, device=config.device)
    gcl_loss = torch.tensor(0.8, device=config.device)
    preference_loss = torch.tensor(0.6, device=config.device)
    rtg_loss = torch.tensor(0.1, device=config.device)
    grad_norm = torch.tensor(2.3, device=config.device)
    expert_predictions = torch.randn(32, device=config.device)
    rtg_labels = torch.randn(32, device=config.device)
    
    sync_count = 0
    no_sync_count = 0
    
    # 模拟100个训练步骤
    for step in range(1, 101):
        if step % config.detailed_log_frequency == 0:
            # 详细统计模式 - 会有.item()调用
            sync_count += 1
            stats = {
                'train/total_loss': total_loss.item(),
                'train/gcl_loss': gcl_loss.item(),
                'train/preference_loss': preference_loss.item(),
                'train/rtg_loss': rtg_loss.item(),
                'train/grad_norm': grad_norm.item(),
                'advantage': expert_predictions.mean().item(),
                'reward_mean': rtg_labels.mean().item(),
            }
        else:
            # 基础统计模式 - 保持tensor在GPU
            no_sync_count += 1
            stats = {
                'train/total_loss': total_loss.detach(),
                'train/gcl_loss': gcl_loss.detach(),
                'train/preference_loss': preference_loss.detach(),
                'train/rtg_loss': rtg_loss.detach(),
            }
    
    print(f"📊 同步频率统计 (100步):")
    print(f"  详细统计步骤: {sync_count} (每步约7次.item()调用)")
    print(f"  基础统计步骤: {no_sync_count} (每步0次.item()调用)")
    print(f"  总GPU同步次数: {sync_count * 7} (vs 原来的700次)")
    print(f"  同步减少: {((700 - sync_count * 7) / 700 * 100):.1f}%")

def test_validation_batch_accumulation():
    """测试验证过程的批量累积"""
    print("\n🔧 测试验证批量累积...")
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    
    # 模拟验证数据
    batch_rewards = []
    for i in range(10):  # 10个验证batch
        batch_rewards.append(torch.randn(16, device=device))  # 每个batch 16个样本
    
    # 传统方式：每个batch都同步
    print("📊 传统验证方式:")
    start_time = time.time()
    total_reward_old = 0
    for rewards in batch_rewards:
        total_reward_old += rewards.sum().item()  # 每次都同步
    avg_reward_old = total_reward_old / (10 * 16)
    old_time = time.time() - start_time
    print(f"  处理时间: {old_time:.4f}s")
    print(f"  GPU同步次数: 10")
    print(f"  平均reward: {avg_reward_old:.4f}")
    
    # 优化方式：批量累积后一次同步
    print("\n📊 优化验证方式:")
    start_time = time.time()
    all_rewards = torch.cat(batch_rewards, dim=0)  # 合并所有tensor
    avg_reward_new = all_rewards.mean().item()  # 只同步一次
    new_time = time.time() - start_time
    print(f"  处理时间: {new_time:.4f}s")
    print(f"  GPU同步次数: 1")
    print(f"  平均reward: {avg_reward_new:.4f}")
    print(f"  性能提升: {((old_time - new_time) / old_time * 100):.1f}%")
    print(f"  结果一致性: {abs(avg_reward_old - avg_reward_new) < 1e-6}")

if __name__ == "__main__":
    print("🚀 RTG训练器性能修复验证")
    print("=" * 50)
    
    if torch.cuda.is_available():
        print(f"✅ 使用GPU: {torch.cuda.get_device_name()}")
    else:
        print("⚠️  使用CPU (GPU不可用)")
    
    try:
        test_loss_function_performance()
        test_trainer_sync_frequency()
        test_validation_batch_accumulation()
        
        print("\n" + "=" * 50)
        print("✅ 所有测试完成！性能优化验证成功")
        print("\n📈 预期性能改善:")
        print("  - 训练阶段GPU同步减少90%")
        print("  - 验证阶段GPU同步减少90%")
        print("  - 损失函数统计计算加速")
        print("  - 三GPU训练速度应恢复正常")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
