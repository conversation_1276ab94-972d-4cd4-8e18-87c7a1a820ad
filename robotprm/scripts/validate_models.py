#!/usr/bin/env python3
"""
Model Validation Script (STRICT MODE)

This script validates a single model checkpoint using the same validation 
metrics as the training script. Follows STRICT MODE principles:
- Fail-fast error handling (no fallback mechanisms)
- Mandatory cache validation for RTG models
- Simplified, focused design

Usage:
    python scripts/validate_models.py --model_path /path/to/model.pt --method iql
    python scripts/validate_models.py --model_path /path/to/model.pt --method rtg --use_shared_memory
"""

import torch
import torch.distributed as dist
import torch.multiprocessing as mp
import sys
import logging
from pathlib import Path
import argparse
import pandas as pd
from omegaconf import DictConfig
import os
import datetime
import tempfile
import json

# Fix for CUDA multiprocessing
mp.set_start_method('spawn', force=True)

# Add src directory to path for imports
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))
sys.path.insert(0, str(project_root))

from trainers.base_trainer import seed_everything
from trainers.rtg_trainer import RTGConfig
from utils.model_utils import create_model, create_trainer, create_training_config
from utils.distributed_utils import create_distributed_frame_batches, aggregate_validation_stats_across_ranks

# Dataset paths (unified with training script)
root = "/mnt/disk2/surui/data"
TRAIN_DATASETS_DIR = Path(f"{root}/CALVIN/task_ABC_D/training")
VAL_DATASETS_DIR = Path(f"{root}/CALVIN/task_ABC_D/validation")

# Observation space configuration (same as training)
OBSERVATION_SPACE = DictConfig({
    "rgb_obs": ['rgb_static', 'rgb_gripper'],
    "depth_obs": [],
    "state_obs": ['robot_obs'],
    "actions": ['rel_actions'],
    "language": ['language']
})
PROPRIO_STATE = DictConfig({
    "n_state_obs": 7,
    "keep_indices": [i for i in range(7)]
})


def setup_logger():
    """Setup logging for validation"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)


def load_model_checkpoint(model_path, method, args, device):
    """Load model checkpoint (STRICT MODE - no fallback)"""
    # Create model
    model = create_model(method, args, device)
    
    # Always wrap with DDP for consistent checkpoint loading
    # Even for single GPU, this ensures compatibility with DDP-trained checkpoints
    from utils.model_utils import create_ddp_model
    import torch.distributed as dist
    
    # Get current rank (0 if not in distributed mode)
    rank = dist.get_rank() if dist.is_initialized() else 0
    ddp_model = create_ddp_model(model, rank)
    
    # Create training configuration
    config = create_training_config(method, args, args.batch_size, device)
    
    # Create trainer with DDP-wrapped model
    trainer = create_trainer(method, ddp_model, config)
    
    # Load checkpoint (now compatible with DDP-trained checkpoints)
    trainer.load_checkpoint(model_path)
    
    # Set to evaluation mode
    if hasattr(trainer, 'q_network'):
        trainer.q_network.eval()
    if hasattr(trainer, 'v_network'):
        trainer.v_network.eval()
    if hasattr(trainer, 'reward_model'):
        trainer.reward_model.eval()
    if hasattr(trainer, 'model'):
        trainer.model.eval()
    
    print(f"✅ Loaded model: {model_path.name}")
    return trainer


def create_validation_dataset(args):
    """Create validation dataset using unified approach"""
    if args.use_shared_memory:
        from datasets.episode_shm_dataset import create_shared_memory_episode_datasets
        
        # RTG STRICT MODE: Configure RTG cache if needed
        rtg_config = None
        if args.method == 'rtg':
            from utils.rtg_cache_manager import RTGCacheManager
            cache_manager = RTGCacheManager(VAL_DATASETS_DIR)
            assert cache_manager.cache_exists(), (
                f"RTG cache required but not found at {VAL_DATASETS_DIR}. "
                f"Run: python scripts/precompute_rtg_cache.py --dataset_path {VAL_DATASETS_DIR}"
            )
            
            # Create RTG config using the authoritative RTGConfig class
            rtg_config = RTGConfig(
                rtg_gamma=getattr(args, 'rtg_gamma', 0.99),
                reward_weights=getattr(args, 'rtg_reward_weights', (0.1, 0.1, 0.01, 0.1)),
                device='cpu'  # RTG Computer uses CPU for preprocessing
            )
        
        # Only load depth data for irl_policy method
        load_depth_data = (args.method == 'irl_policy')
        
        # Enable RTG computation for RTG method
        compute_rtg_labels = (args.method == 'rtg')
        
        _, val_dataset = create_shared_memory_episode_datasets(
            TRAIN_DATASETS_DIR,
            VAL_DATASETS_DIR,
            OBSERVATION_SPACE,
            PROPRIO_STATE,
            gamma=0.99,
            debug_max_episodes=args.max_episodes,
            reward_scale=1.0,
            reward_type=args.reward_type,
            sparse_reward_steps=args.sparse_reward_steps,
            load_depth_data=load_depth_data,
            compute_rtg_labels=compute_rtg_labels,
            rtg_config=rtg_config,
            only_val=True,
        )
    else:
        from datasets.episode_dataset import create_episode_datasets
        
        # STRICT MODE: RTG method requires RTG cache
        rtg_config = None
        if args.method == 'rtg':
            from utils.rtg_cache_manager import RTGCacheManager
            cache_manager = RTGCacheManager(VAL_DATASETS_DIR)
            assert cache_manager.cache_exists(), (
                f"RTG cache required but not found at {VAL_DATASETS_DIR}. "
                f"Run: python scripts/precompute_rtg_cache.py --dataset_path {VAL_DATASETS_DIR}"
            )
            
            # Create RTG config for cache validation
            rtg_config = RTGConfig(
                rtg_gamma=getattr(args, 'rtg_gamma', 0.99),
                reward_weights=getattr(args, 'rtg_reward_weights', (0.1, 0.1, 0.01, 0.1)),
                device='cpu'
            )
            compute_rtg_labels = True
        else:
            compute_rtg_labels = False
        
        _, val_dataset = create_episode_datasets(
            TRAIN_DATASETS_DIR,
            VAL_DATASETS_DIR,
            OBSERVATION_SPACE,
            PROPRIO_STATE,
            gamma=0.99,
            debug_max_episodes=args.max_episodes,
            reward_scale=1.0,
            reward_type=args.reward_type,
            sparse_reward_steps=args.sparse_reward_steps,
            compute_rtg_labels=compute_rtg_labels,
            rtg_config=rtg_config
        )
    
    return val_dataset


def validate_model(trainer, val_dataset, args, rank=0, world_size=1):
    """Validate model and return metrics"""
    dataset_type = 'shm' if args.use_shared_memory else 'episode'
    
    # Create distributed validation batches
    val_batch_generator, val_batch_count = create_distributed_frame_batches(
        val_dataset,
        args.batch_size,
        rank=rank,
        world_size=world_size,
        shuffle=False,
        epoch=0
    )
    
    # Run validation
    val_stats = trainer.validate(
        val_batch_generator,
        epoch=1,
        dataset_type=dataset_type,
        valida_len=val_batch_count
    )
    
    return val_stats


def process_validation_results(val_stats, model_path, method):
    """Process validation results from different trainer types"""
    if val_stats is None:
        return None
    
    # Basic metrics that all trainers should have
    base_metrics = {
        'model_name': model_path.name,
        'method': method,
        'num_samples': val_stats.get('val/num_samples', 0)
    }
    
    # Handle RTG trainer's multi-strategy metrics
    if method == 'rtg':
        # RTG trainer returns metrics like: val/top1_acc/cost_only, val/top1_acc/reward_only, etc.
        strategies = ['cost_only', 'reward_only', 'cost_reward_combined']
        
        # Extract multi-strategy metrics
        for strategy in strategies:
            for metric in ['top1_acc', 'mrr', 'ranking_consistency']:
                key = f'val/{metric}/{strategy}'
                if key in val_stats:
                    base_metrics[f'{metric}_{strategy}'] = val_stats[key]
        
        # Use cost_reward_combined as the main metrics for compatibility
        base_metrics['top1_acc'] = val_stats.get('val/top1_acc/cost_reward_combined', 0.0)
        base_metrics['mrr'] = val_stats.get('val/mrr/cost_reward_combined', 0.0)
        base_metrics['ranking_consistency'] = val_stats.get('val/ranking_consistency/cost_reward_combined', 0.0)
        
        # Add RTG-specific metrics
        if 'val/gcl_loss' in val_stats:
            base_metrics['gcl_loss'] = val_stats['val/gcl_loss']
        if 'val/rtg_loss' in val_stats:
            base_metrics['rtg_loss'] = val_stats['val/rtg_loss']
    
    else:
        # Standard trainer metrics (IQL, CQL, IRL)
        base_metrics['top1_acc'] = val_stats.get('val/top1_acc', 0.0)
        base_metrics['mrr'] = val_stats.get('val/mrr', 0.0)
        base_metrics['ranking_consistency'] = val_stats.get('val/ranking_consistency', 0.0)
    
    # Add method-specific metrics
    if 'val/q_mean' in val_stats:
        base_metrics['q_mean'] = val_stats['val/q_mean']
    if 'val/v_mean' in val_stats:
        base_metrics['v_mean'] = val_stats['val/v_mean']
    if 'val/advantage' in val_stats:
        base_metrics['advantage'] = val_stats['val/advantage']
    if 'val/cost_mean' in val_stats:
        base_metrics['cost_mean'] = val_stats['val/cost_mean']
    if 'val/expert_cost_mean' in val_stats:
        base_metrics['expert_cost_mean'] = val_stats['val/expert_cost_mean']
    
    return base_metrics


def distributed_validation_worker(rank, world_size, model_path, args_dict, port, result_file):
    """Worker function for distributed validation"""
    # Setup distributed environment
    os.environ['MASTER_ADDR'] = 'localhost'
    os.environ['MASTER_PORT'] = port
    os.environ['NCCL_DEBUG'] = 'WARN'
    
    dist.init_process_group("nccl", rank=rank, world_size=world_size,
                           timeout=datetime.timedelta(minutes=30))
    torch.cuda.set_device(rank)
    
    # Set random seed
    seed_everything(42, rank)
    
    device = torch.device(f"cuda:{rank}")
    
    # Convert args_dict to object
    class Args:
        def __init__(self, d):
            for k, v in d.items():
                setattr(self, k, v)
    
    args = Args(args_dict)
    
    # Create validation dataset
    val_dataset = create_validation_dataset(args)
    
    # Load model
    trainer = load_model_checkpoint(model_path, args.method, args, device)
    
    # Run validation
    local_val_stats = validate_model(trainer, val_dataset, args, rank, world_size)
    
    # Aggregate results
    aggregated_stats = aggregate_validation_stats_across_ranks(
        local_val_stats, rank, world_size
    )
    
    # Only rank 0 saves the results
    if rank == 0:
        with open(result_file, 'w') as f:
            json.dump(aggregated_stats, f)
    
    # Cleanup
    dist.destroy_process_group()


def main():
    parser = argparse.ArgumentParser(description='Validate Single Model (STRICT MODE)')
    
    # Required arguments
    parser.add_argument('--model_path', type=str, required=True,
                       help='Path to model checkpoint file')
    parser.add_argument('--method', type=str, required=True,
                       choices=['iql', 'cql', 'irl', 'irl_policy', 'rtg'],
                       help='Training method')
    parser.add_argument('--total_epochs', type=int, default=10,
                       help='Total epochs')
    
    # Model parameters
    parser.add_argument('--hidden_size', type=int, default=512,
                       help='Hidden size (must match training)')
    parser.add_argument('--num_layers', type=int, default=12,
                       help='Number of layers (must match training)')
    parser.add_argument('--sequence_length', type=int, default=10,
                       help='Sequence length for RTG models')
    
    # Validation parameters
    parser.add_argument('--batch_size', type=int, default=64,
                       help='Validation batch size')
    parser.add_argument('--max_episodes', type=int, default=None,
                       help='Maximum episodes to validate')
    parser.add_argument('--use_shared_memory', action='store_true',
                       help='Use shared memory dataset')
    
    # Dataset parameters
    parser.add_argument('--reward_type', type=str, default='dense',
                       choices=['dense', 'sparse'],
                       help='Reward type')
    parser.add_argument('--sparse_reward_steps', type=int, default=1,
                       help='Sparse reward steps')
    
    # Method-specific parameters
    parser.add_argument('--learning_rate_q', type=float, default=3e-4,
                       help='Q learning rate')
    parser.add_argument('--learning_rate_v', type=float, default=2e-4,
                       help='V learning rate')
    parser.add_argument('--tau', type=float, default=0.7,
                       help='IQL expectile parameter')
    parser.add_argument('--cql_alpha', type=float, default=1.0,
                       help='CQL regularization weight')
    parser.add_argument('--cql_n_actions', type=int, default=5,
                       help='CQL number of actions')
    parser.add_argument('--use_double_q', action='store_true',
                       help='Use double Q-learning')
    
    # RTG-specific parameters
    parser.add_argument('--rtg_weight', type=float, default=1.0,
                       help='RTG weight')
    parser.add_argument('--preference_weight', type=float, default=1.0,
                       help='RTG preference weight')
    parser.add_argument('--rtg_tau', type=float, default=0.7,
                       help='RTG expectile parameter')
    parser.add_argument('--rtg_gamma', type=float, default=0.99,
                       help='RTG discount factor')
    parser.add_argument('--rtg_reward_weights', type=tuple, default=(0.1, 0.1, 0.01, 0.1),
                       help='RTG reward weights')
    parser.add_argument('--pretrained_gr1_path', type=str, default=None,
                       help='Path to pretrained GR1 weights')
    parser.add_argument('--img_feat_dim', type=int, default=768,
                       help='Image feature dimension')
    parser.add_argument('--patch_feat_dim', type=int, default=768,
                       help='Patch feature dimension')
    parser.add_argument('--use_hand_rgb', action='store_true', default=True,
                       help='Use hand RGB images')
    parser.add_argument('--clip_backbone', type=str, default='ViT-B/32',
                       help='CLIP backbone model')
    parser.add_argument('--mae_ckpt', type=str, default="/mnt/disk2/surui/BoostingVLA/GR-1/logs/mae_pretrain_vit_base.pth",
                       help='Path to MAE checkpoint')
    
    # Distributed options
    parser.add_argument('--world_size', type=int, default=1,
                       help='Number of GPUs for distributed validation')
    
    # Output options
    parser.add_argument('--output_csv', type=str, default=None,
                       help='CSV output file')
    parser.add_argument('--output_dir', type=str, default='results',
                       help='Output directory')
    
    args = parser.parse_args()

    # args中train才需要的参数. 在validate中固定
    args.gcl_weight = 0.1
    args.preference_weight = 0.8
    args.rtg_weight = 0.1
    
    # Setup
    logger = setup_logger()
    seed_everything(42)
    
    # Validate model path
    model_path = Path(args.model_path)
    assert model_path.exists(), f"Model file not found: {args.model_path}"
    
    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Auto-generate CSV filename if not provided
    if args.output_csv is None:
        model_name = model_path.stem
        clean_name = model_name.replace('_checkpoint_epoch_', '_ep').replace('_unified', '')
        args.output_csv = str(output_dir / f"{clean_name}_validation.csv")
    
    print(f"🔍 Validating: {model_path.name}")
    print(f"📊 Method: {args.method}")
    print(f"💾 Dataset: {'Shared Memory' if args.use_shared_memory else 'Episode-based'}")
    print(f"🚀 GPUs: {args.world_size}")
    
    # Run validation - Always use distributed path for consistent DDP handling
    print(f"🔄 Running {'distributed' if args.world_size > 1 else 'single GPU'} validation with DDP compatibility")
    
    # Create temporary file for result passing
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.json') as temp_file:
        result_file = temp_file.name
    
    try:
        # Convert args to dict for serialization
        args_dict = vars(args)
        port = str(12355 + (hash(str(model_path)) % 1000))
        
        # Always use mp.spawn for consistent DDP model loading
        mp.spawn(
            distributed_validation_worker,
            args=(args.world_size, model_path, args_dict, port, result_file),
            nprocs=args.world_size,
            join=True
        )
        
        # Read results from temporary file
        val_stats = None
        if os.path.exists(result_file):
            with open(result_file, 'r') as f:
                val_stats = json.load(f)
        
    finally:
        # Clean up temporary file
        if os.path.exists(result_file):
            os.unlink(result_file)
    
    # Process results
    metrics = process_validation_results(val_stats, model_path, args.method)
    
    if metrics:
        # Print results
        print("\n" + "="*60)
        print("VALIDATION RESULTS")
        print("="*60)
        print(f"Model: {metrics['model_name']}")
        print(f"Method: {metrics['method']}")
        print(f"Top1 Accuracy: {metrics['top1_acc']:.4f}")
        print(f"MRR: {metrics['mrr']:.4f}")
        print(f"Ranking Consistency: {metrics['ranking_consistency']:.4f}")
        print(f"Samples: {metrics['num_samples']}")
        
        # Print method-specific metrics
        if args.method == 'rtg':
            print("\nRTG Multi-Strategy Results:")
            strategies = ['cost_only', 'reward_only', 'cost_reward_combined']
            for strategy in strategies:
                if f'top1_acc_{strategy}' in metrics:
                    print(f"  {strategy}: Top1={metrics[f'top1_acc_{strategy}']:.4f}, "
                          f"MRR={metrics[f'mrr_{strategy}']:.4f}, "
                          f"Consistency={metrics[f'ranking_consistency_{strategy}']:.4f}")
        
        # Save to CSV
        df = pd.DataFrame([metrics])
        df.to_csv(args.output_csv, index=False)
        print(f"💾 Results saved: {args.output_csv}")
        
    else:
        print("❌ Validation failed")
        
        # Save failed result
        failed_metrics = {
            'model_name': model_path.name,
            'method': args.method,
            'top1_acc': 0.0,
            'mrr': 0.0,
            'ranking_consistency': 0.0,
            'num_samples': 0,
            'status': 'FAILED'
        }
        df = pd.DataFrame([failed_metrics])
        df.to_csv(args.output_csv, index=False)
        print(f"💾 Failed result saved: {args.output_csv}")


if __name__ == "__main__":
    main()