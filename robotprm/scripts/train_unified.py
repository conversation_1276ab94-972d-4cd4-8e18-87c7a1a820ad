"""
Unified Multi-GPU Training Script

This script unifies all training approaches with command-line control:
- Regular EpisodeBasedDataset vs SharedMemoryEpisodeDataset
- Configurable GPU count via --world_size
- All training parameters controllable via command line
- Support for IQL, CQL, and IRL training methods
"""

import torch
import torch.distributed as dist
import torch.multiprocessing as mp
import sys
import logging
import swanlab
from tqdm import tqdm
import os
from pathlib import Path
import argparse
import datetime

# Set distributed communication timeout to handle large dataset loading
os.environ['NCCL_TIMEOUT'] = '7200'  # 2 hour timeout
os.environ['NCCL_BLOCKING_WAIT'] = '1'  # More stable communication
os.environ['NCCL_ASYNC_ERROR_HANDLING'] = '1'

# Add src directory to path for imports
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))
sys.path.insert(0, str(project_root))

# Import from reorganized project structure
from trainers.base_trainer import seed_everything
from trainers.rtg_trainer import RTGConfig
from utils.preprocessing import preprocess_batch_for_training
from utils.model_utils import (
    create_model, create_ddp_model, log_model_stats,
    create_trainer, create_training_config, log_training_config
)
from utils.distributed_utils import (
    create_distributed_frame_batches, aggregate_validation_stats_across_ranks
)
from omegaconf import DictConfig


def setup_distributed(rank, world_size, port="12355"):
    """Initialize distributed training environment"""
    os.environ['MASTER_ADDR'] = 'localhost'
    os.environ['MASTER_PORT'] = port
    
    # Reduce NCCL verbosity
    os.environ['NCCL_DEBUG'] = 'WARN'
    os.environ['NCCL_DEBUG_SUBSYS'] = 'OFF'
    
    # Initialize process group with extended timeout for shared memory loading
    import datetime
    timeout = datetime.timedelta(minutes=120)  # 120 minutes timeout for large dataset loading
    dist.init_process_group("nccl", rank=rank, world_size=world_size, timeout=timeout)
    torch.cuda.set_device(rank)


def cleanup_distributed():
    """Clean up distributed training environment"""
    dist.destroy_process_group()


def setup_logger(rank, dataset_type):
    """Setup logging - minimal output, only errors"""
    logger = logging.getLogger(__name__)
    # 设置为ERROR级别，只显示错误信息
    logging.basicConfig(level=logging.ERROR)
    return logger


def train_worker(rank, world_size, args):
    """Unified training worker function for each GPU"""
    
    # Set random seed for reproducibility with rank-specific diversity
    seed_everything(42, rank)
    
    # Setup distributed environment
    setup_distributed(rank, world_size, args.port)
    dataset_type = 'shm' if args.shm else 'episode'
    setup_logger(rank, dataset_type)
    
    # Training configuration
    max_episodes_train = args.debug_max_episodes if args.debug_only else None
    max_episodes_val = args.debug_max_episodes // 5 if args.debug_only else None
    
    # 保存路径配置 - debug模式使用Debug文件夹
    # 生成时间戳（仅日期）
    date_timestamp = datetime.datetime.now().strftime("%Y%m%d")
    
    # 生成learning rate标识符（根据不同方法使用不同的学习率）
    lr_q_str = f"{args.learning_rate_q:.0e}".replace('e-0', 'e').replace('e-', 'e')
    lr_v_str = f"{args.learning_rate_v:.0e}".replace('e-0', 'e').replace('e-', 'e')
    
    if args.method in ['irl', 'rtg']:
        # IRL, RTG只使用一个学习率（learning_rate_q）
        lr_suffix = f"_lr{lr_q_str}"
    else:
        # 默认情况使用单一学习率
        lr_suffix = f"_lr{lr_q_str}"
    
    if args.debug_only:
        save_folder_name = f"Debug"
    else:
        # 根据reward配置生成不同的文件夹名称，并添加时间戳和learning rate
        if args.reward_type == 'dense':
            save_folder_name = f"{args.method}_dense{lr_suffix}_{date_timestamp}"
        elif args.reward_type == 'sparse':
            save_folder_name = f"{args.method}_sparse_{args.sparse_reward_steps}{lr_suffix}_{date_timestamp}"
        else:
            save_folder_name = f"{args.method}_{args.reward_type}{lr_suffix}_{date_timestamp}"
    
    # Initialize SwanLab only on main process
    if rank == 0:
        # 生成实验名称 - 包含reward配置信息和learning rate
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        if args.reward_type == 'dense':
            exp_name = f"{args.method}_dense{lr_suffix}_{timestamp}"
        elif args.reward_type == 'sparse':
            exp_name = f"{args.method}_sparse_{args.sparse_reward_steps}{lr_suffix}_{timestamp}"
        else:
            exp_name = f"{args.method}_{args.reward_type}{lr_suffix}_{timestamp}"
        
        swanlab.init(
            project="RobotPRM" if not args.debug_only else "Debug",
            name=exp_name,  # 指定实验名称
            group=args.method,
            config={
                "per_gpu_bs": args.per_gpu_bs,
                "total_batch_size": args.batch_size,
                "total_epochs": args.total_epochs,
                "learning_rate_q": args.learning_rate_q,
                "learning_rate_v": args.learning_rate_v,
                "tau": args.tau,
                "gamma": 0.99,
                "hidden_size": args.hidden_size,
                "num_layers": args.num_layers,
                "world_size": world_size,
                "dataset_type": dataset_type,
                "use_shared_memory": args.shm,
                "max_episodes_train": max_episodes_train,
                "max_episodes_val": max_episodes_val,
                "method": args.method,
                "reward_type": args.reward_type,
                "sparse_reward_steps": args.sparse_reward_steps,
                # 添加损失函数配置 - 作为实验配置而非训练指标
                "loss_function": getattr(args, 'loss_function', 'bradley_terry'),
            }
        )
    
    root = "/mnt/disk2/surui/data"
    # Dataset paths
    TRAIN_DATASETS_DIR = Path(f"{root}/CALVIN/task_ABC_D/training")
    VAL_DATASETS_DIR = Path(f"{root}/CALVIN/task_ABC_D/validation")
    
    # Debug: Print dataset paths for RTG cache verification
    if rank == 0:
        print(f"📁 Dataset paths:")
        print(f"   Training: {TRAIN_DATASETS_DIR}")
        print(f"   Validation: {VAL_DATASETS_DIR}")
        print(f"   RTG cache (train): {TRAIN_DATASETS_DIR}/rtg_cache")
        print(f"   RTG cache (val): {VAL_DATASETS_DIR}/rtg_cache")
    
    # Observation space configuration
    OBSERVATION_SPACE = DictConfig({
        "rgb_obs": ['rgb_static', 'rgb_gripper'],
        "depth_obs": [],
        "state_obs": ['robot_obs'],
        "actions": ['rel_actions'],
        "language": ['language']
    })
    PROPRIO_STATE = DictConfig({
        "n_state_obs": 7,
        "keep_indices": [i for i in range(7)]
    })
    
    # Create datasets based on type
    if args.shm:
        # Use shared memory datasets
        if rank == 0:
            from datasets.episode_shm_dataset import create_shared_memory_episode_datasets
            
            # Only load depth data for irl_policy method (已移除)
            load_depth_data = False
            
            rtg_config = RTGConfig(
                rtg_gamma=getattr(args, 'rtg_gamma', 0.99),
                reward_weights=getattr(args, 'rtg_reward_weights', (0.1, 0.1, 0.01, 0.1)),
                device='cpu'  # RTG Computer uses CPU for preprocessing
            )
            
            train_dataset, val_dataset = create_shared_memory_episode_datasets(
                TRAIN_DATASETS_DIR,
                VAL_DATASETS_DIR,
                OBSERVATION_SPACE,
                PROPRIO_STATE,
                gamma=0.99,
                debug_max_episodes=max_episodes_train,
                reward_scale=1.0,
                reward_type=args.reward_type,
                sparse_reward_steps=args.sparse_reward_steps,
                load_depth_data=load_depth_data,
                compute_rtg_labels=True,  # 始终计算RTG标签
                rtg_config=rtg_config
            )
        else:
            # Worker processes wait for main process to complete shared memory creation
            pass
        
        # Synchronize all processes (critical for DDP)
        dist.barrier()
        
        if rank != 0:
            # Worker processes connect to existing shared memory
            from datasets.episode_shm_dataset import SharedMemoryEpisodeDataset
            
            train_dataset = SharedMemoryEpisodeDataset(
                datasets_dir=TRAIN_DATASETS_DIR,
                obs_space=OBSERVATION_SPACE,
                proprio_state=PROPRIO_STATE,
                shm_prefix="train_episodes",
                preload_to_shm=False
            )
            train_dataset.connect_to_shared_memory()
            
            val_dataset = SharedMemoryEpisodeDataset(
                datasets_dir=VAL_DATASETS_DIR,
                obs_space=OBSERVATION_SPACE,
                proprio_state=PROPRIO_STATE,
                shm_prefix="val_episodes",
                preload_to_shm=False
            )
            val_dataset.connect_to_shared_memory()
            
            # Verify connection by checking dataset length
            if len(train_dataset) == 0 or len(val_dataset) == 0:
                raise RuntimeError("Connected to shared memory but datasets are empty")

    else:
        # Use regular episode datasets
        from datasets.episode_dataset import create_episode_datasets
        
        # STRICT MODE: RTG method requires RTG cache
        # Create RTG config for cache validation
        rtg_config = RTGConfig(
            rtg_gamma=getattr(args, 'rtg_gamma', 0.99),
            reward_weights=getattr(args, 'rtg_reward_weights', (0.1, 0.1, 0.01, 0.1)),
            device='cpu'
        )
        compute_rtg_labels = True  # 始终计算RTG标签
        
        train_dataset, val_dataset = create_episode_datasets(
            TRAIN_DATASETS_DIR,
            VAL_DATASETS_DIR,
            OBSERVATION_SPACE,
            PROPRIO_STATE,
            gamma=0.99,
            debug_max_episodes=max_episodes_train,
            reward_scale=1.0,
            reward_type=args.reward_type,
            sparse_reward_steps=args.sparse_reward_steps,
            compute_rtg_labels=compute_rtg_labels,
            rtg_config=rtg_config
        )
    
    # Use per GPU batch size directly
    train_batch_size = args.per_gpu_bs
    
    # Create unified model and trainer
    device = torch.device(f"cuda:{rank}")
    
    # Log training configuration
    log_training_config(args.method, args, rank)
    
    # Create unified model
    model = create_model(args.method, args, device)
    
    # Wrap with DDP
    ddp_model = create_ddp_model(model, rank)
    
    # Create training configuration
    config = create_training_config(args.method, args, train_batch_size, device)
    
    # Create trainer
    trainer = create_trainer(args.method, ddp_model, config)
    
    # Log model statistics
    log_model_stats(model, args.method, rank)
    
    # Get expected batch count for step calculation (needed for step-based scheduling)
    _, expected_batches_for_rank = create_distributed_frame_batches(
        train_dataset, 
        train_batch_size, 
        rank, 
        world_size, 
        shuffle=False,  # Doesn't matter for count calculation
        epoch=0         # Doesn't matter for count calculation
    )
    
    # Calculate total steps for step-based scheduling (all methods now use step-based)
    total_steps = expected_batches_for_rank * args.total_epochs
    trainer.set_total_steps(total_steps)
    
    # Training loop
    total_epochs = args.total_epochs
    save_freq = args.save_freq if args.save_freq is not None else 2
    val_freq = args.val_freq if args.val_freq is not None else 2
    
    # Epoch progress bar
    epoch_pbar = None
    if rank == 0:
        epoch_pbar = tqdm(range(total_epochs), total=total_epochs, desc="Training Progress", ncols=100)
    
    for epoch in (epoch_pbar if rank == 0 else range(total_epochs)):
        if rank == 0 and epoch_pbar is not None:
            epoch_pbar.set_description(f"Epoch {epoch+1}/{total_epochs}")
        
        # Set current epoch for curriculum learning (e.g., in IRL trainer)
        trainer.set_epoch(epoch)
        
        epoch_stats = {
            'epoch_q_loss': 0,
            'epoch_v_loss': 0,
            'epoch_advantage': 0,
            'num_batches': 0
        }
        
        # Training phase with batch progress bar
        batch_pbar = None
        batch_idx = 0
        # Use distributed batch generator for proper DDP data partitioning
        batch_generator, expected_batches_for_rank = create_distributed_frame_batches(
            train_dataset, 
            train_batch_size, 
            rank, 
            world_size, 
            shuffle=True, 
            epoch=epoch
        )
        if rank == 0:
            batch_pbar = tqdm(
                total=expected_batches_for_rank,
                desc=f"Epoch {epoch+1} Batches (Rank {rank})", 
                leave=False, 
                ncols=120,
                unit="batch"
            )

        
        for batch_data in batch_generator:
            # Preprocess batch data
            # irl_policy method已移除，不需要pointcloud
            enable_pointcloud = False
            processed_batch = preprocess_batch_for_training(
                batch_data, 
                device=device,
                dataset_type=dataset_type,
                enable_pointcloud=enable_pointcloud
            )
            
            # Training step
            stats = trainer.train_step(processed_batch)
            
            # Step scheduler after each batch (step-based scheduling for all methods)
            trainer.step_schedulers()
            
            # Accumulate statistics (safe access)
            if 'q_loss' in stats:
                epoch_stats['epoch_q_loss'] += stats['q_loss']
            if 'v_loss' in stats:
                epoch_stats['epoch_v_loss'] += stats['v_loss']
            if 'advantage' in stats:
                epoch_stats['epoch_advantage'] += stats['advantage']
            epoch_stats['num_batches'] += 1
            
            # Update batch progress bar
            if rank == 0 and batch_pbar is not None:
                batch_pbar.update(1)
                
                # Dynamic progress bar display
                postfix_data = {}
                if 'q_loss' in stats:
                    postfix_data['Q_loss'] = f"{stats['q_loss']:.4f}"
                if 'v_loss' in stats:
                    postfix_data['V_loss'] = f"{stats['v_loss']:.4f}"
                if 'advantage' in stats:
                    postfix_data['Adv'] = f"{stats['advantage']:.4f}"
                
                # Add method-specific metrics to progress bar
                # CQL method removed, no special handling needed
                
                batch_pbar.set_postfix(postfix_data)
            
            # Log to SwanLab (main process only)
            if rank == 0 and batch_idx % 10 == 0:
                # 直接从trainer的返回值中提取带有train/前缀的字段进行log
                log_data = {}
                
                # 提取所有带有train/前缀的字段
                for key, value in stats.items():
                    if key.startswith('train/'):
                        log_data[key] = value
                                    
                    # Add learning rate if available
                    current_lrs = trainer.get_current_lrs()
                    if len(current_lrs) >= 1:
                        log_data['train/learning_rate'] = current_lrs[0]
                
                if log_data:  # Only log if we have data
                    swanlab.log(log_data)
            
            batch_idx += 1
        
        # Close batch progress bar
        if rank == 0 and batch_pbar is not None:
            batch_pbar.close()
        
        # Synchronize all processes
        dist.barrier()
        
        # Calculate average epoch statistics
        if epoch_stats['num_batches'] > 0:
            for key in ['epoch_q_loss', 'epoch_v_loss', 'epoch_advantage']:
                epoch_stats[key] /= epoch_stats['num_batches']
        
        # Distributed validation phase - all ranks participate
        if (epoch + 1) % val_freq == 0:
            # Create distributed validation batches - each rank gets different data slice
            val_batch_generator, valida_len = create_distributed_frame_batches(
                val_dataset, 
                train_batch_size,  # Use same batch size as training
                rank, 
                world_size, 
                shuffle=False,     # No shuffling for validation
                epoch=0           # Fixed epoch for consistency
            )
            
            # Each rank validates its own data slice
            local_val_stats = trainer.validate(
                val_batch_generator, 
                epoch+1, 
                dataset_type,
                valida_len
            )
            
            # Aggregate results from all ranks
            aggregated_val_stats = aggregate_validation_stats_across_ranks(
                local_val_stats, rank, world_size
            )
            
            # Only rank 0 logs the final aggregated results
            if rank == 0:
                # 直接log验证结果，trainer已经返回了带有val/前缀的字段
                swanlab.log(aggregated_val_stats)
        
        # Save checkpoint (main process only) - silent
        if rank == 0 and (epoch + 1) % save_freq == 0:
            output_dir = Path(f"{root}/Experiment_data/PRMs/{save_folder_name}")
            output_dir.mkdir(parents=True, exist_ok=True)
            checkpoint_path = output_dir / f"{args.method}_unified_checkpoint_epoch_{epoch+1}.pt"
            trainer.save_checkpoint(checkpoint_path)
        
        # Log epoch statistics (main process only)
        if rank == 0:
            # Get current learning rates for logging
            current_lrs = trainer.get_current_lrs()
            
            # Epoch-level统计信息 - 仅记录legacy兼容性字段的epoch汇总
            epoch_log_data = {
                'train/epoch_q_loss': epoch_stats['epoch_q_loss'],
                'train/epoch_v_loss': epoch_stats['epoch_v_loss'],
                'train/epoch_advantage': epoch_stats['epoch_advantage'],
                'train/epoch': epoch + 1,
            }
            
            # Log learning rates (IRL和RTG都使用单一学习率)
            if len(current_lrs) >= 1:
                epoch_log_data['train/epoch_learning_rate'] = current_lrs[0]
                
            swanlab.log(epoch_log_data)
        
        # Learning rate schedulers are stepped per batch (step-based scheduling for all methods)
        # No need to step at epoch end
    
    # Close epoch progress bar
    if rank == 0 and epoch_pbar is not None:
        epoch_pbar.close()
    
    # Save final model (main process only) - silent
    if rank == 0:
        output_dir = Path(f"{root}/Experiment_data/PRMs/{save_folder_name}")
        final_checkpoint_path = output_dir / f"{args.method}_unified_final_model.pt"
        trainer.save_checkpoint(final_checkpoint_path)
        
        swanlab.finish()
    
    # Clean up distributed environment
    cleanup_distributed()


def main():
    parser = argparse.ArgumentParser(description='Unified Multi-GPU Training Script')
    
    # Core training parameters (优化默认值)
    parser.add_argument('--world_size', type=int, default=4, help='Number of GPUs to use')
    parser.add_argument('--port', type=str, default='12355', help='Master port for distributed training')
    parser.add_argument('--per_gpu_bs', type=int, default=80, help='Batch size per GPU (total batch size = per_gpu_bs * world_size)')
    parser.add_argument('--total_epochs', type=int, default=30, help='Total training epochs')
    
    # Model parameters (固化GR1架构参数)
    # hidden_size, num_layers 等GR1架构参数已固化，不再通过命令行配置
    
    # Learning parameters
    parser.add_argument('--learning_rate_q', type=float, default=3e-4, help='Learning rate (统一learning rate)')
    # learning_rate_v, tau 等IQL特定参数已移除
    
    # Training method selection
    parser.add_argument('--method', type=str, default='rtg',
                       choices=['irl', 'rtg'],
                       help='Training method: irl (Inverse Reinforcement Learning), rtg (ReturnToGo with dual-head GR1)')
    
    # Dataset selection (默认启用shared memory)
    parser.add_argument('--shm', action='store_true', default=False, help='Use SharedMemoryEpisodeDataset')
    
    # Reward configuration
    parser.add_argument('--reward_type', type=str, default='dense', 
                       choices=['dense', 'sparse'],
                       help='Type of reward calculation: dense (γ^(T-t-1)), sparse (last N steps get +1)')
    parser.add_argument('--sparse_reward_steps', type=int, default=1,
                       help='Number of steps to give reward in sparse mode (1=only last step, 3=last 3 steps, etc.)')
    
    # CQL参数已移除（方法已废弃）
    
    # RTG/IRL损失权重参数 (核心可调参数)
    parser.add_argument('--gcl_weight', type=float, default=0.0,
                       help='Weight for GCL loss (适用于RTG和IRL方法)')
    parser.add_argument('--preference_weight', type=float, default=1.0,
                       help='Weight for preference loss (适用于RTG和IRL方法)')
    parser.add_argument('--rtg_weight', type=float, default=0.1,
                       help='Weight for RTG expectile regression loss (仅RTG方法)')
    parser.add_argument('--rtg_tau', type=float, default=0.7,
                       help='Expectile parameter for RTG regression (仅RTG方法)')
    
    # RTG固定参数已移除 (rtg_gamma, rtg_warmup_steps, sequence_length, img_feat_dim, patch_feat_dim, use_hand_rgb, clip_backbone, mae_ckpt, pretrained_gr1_path)
    
    # Debug and validation
    parser.add_argument('--debug_only', action='store_true', help='Debug mode with limited episodes')
    parser.add_argument('--debug_max_episodes', type=int, default=1024, help='Max episodes for debugging')
    parser.add_argument('--val_freq', type=int, default=1, help='Validation frequency')
    parser.add_argument('--save_freq', type=int, default=1)
    
    args = parser.parse_args()
    
    # 计算总batch size
    args.batch_size = args.per_gpu_bs * args.world_size
    
    # 固化GR1架构参数（不再通过命令行配置）
    args.hidden_size = 384      # GR1固定架构
    args.num_layers = 12        # GR1固定架构
    args.sequence_length = 10   # GR1固定架构
    args.img_feat_dim = 768     # GR1固定架构
    args.patch_feat_dim = 768   # GR1固定架构
    args.use_hand_rgb = True    # 默认启用gripper camera
    args.clip_backbone = 'ViT-B/32'  # 默认CLIP backbone
    args.mae_ckpt = "/mnt/disk2/surui/BoostingVLA/GR-1/logs/mae_pretrain_vit_base.pth"        # 默认不使用MAE checkpoint
    args.pretrained_gr1_path = "/mnt/disk2/surui/BoostingVLA/GR-1/logs/snapshot_ABC.pt"  # 默认不使用预训练GR1权重
    
    # 固化RTG特定参数
    args.rtg_gamma = 0.99       # RTG discount factor
    args.rtg_warmup_steps = 1000  # RTG warmup steps
    args.rtg_reward_weights = (0.1, 0.1, 0.01, 0.1)  # RTG dense reward weights
    
    # 固化IQL参数（向后兼容）
    args.learning_rate_v = args.learning_rate_q  # IQL V网络学习率与Q网络一致
    args.tau = 0.7  # IQL expectile parameter
    
    # 固化CQL参数（向后兼容）
    args.cql_alpha = 1.0
    args.cql_n_actions = 5
    args.cql_freq = 1
    args.use_double_q = False
    
    # Check GPU availability
    if not torch.cuda.is_available():
        raise RuntimeError("CUDA is not available")
    
    available_gpus = torch.cuda.device_count()
    if args.world_size > available_gpus:
        print(f"Warning: Requested {args.world_size} GPUs but only {available_gpus} available")
        args.world_size = available_gpus
    
    dataset_name = "Shared Memory" if args.shm else "Episode-based"
    
    method_name = {"irl": "IRL", "rtg": "RTG"}.get(args.method, args.method.upper())
    
    print(f"🚀 Starting unified multi-GPU {method_name} training")
    print(f"💾 Dataset type: {dataset_name}")
    method_params = ""
    if args.method == 'rtg':
        method_params = f" (rtg_w={args.rtg_weight}, preference_w={args.preference_weight}, τ={args.rtg_tau})"
    elif args.method == 'irl':
        method_params = f" (preference_w={args.preference_weight})"
    else:
        method_params = ""
    print(f"🧬 Method: {args.method.upper()}{method_params}")
    print(f"📊 Using {args.world_size} GPUs")
    print(f"🧠 Model: hidden_size={args.hidden_size}, num_layers={args.num_layers}")
    print(f"📦 Batch size: {args.per_gpu_bs} per GPU ({args.batch_size} total)")
    print(f"🎯 Episodes: {'Limited for debugging' if args.debug_only else 'All available'}")
    print(f"🏆 Reward type: {args.reward_type}" + (f" (last {args.sparse_reward_steps} steps)" if args.reward_type == 'sparse' else ""))
    
    if args.shm:
        print(f"⚡ Shared memory provides 10x+ speedup for multi-GPU training")
    
    # Launch multi-process training
    mp.spawn(
        train_worker,
        args=(args.world_size, args),
        nprocs=args.world_size,
        join=True
    )


if __name__ == "__main__":
    main()