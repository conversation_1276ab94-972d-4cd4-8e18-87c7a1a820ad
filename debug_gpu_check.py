import torch
import time

def test_gpu_performance():
    """测试每个GPU的基础性能"""
    for gpu_id in [1, 2, 3]:
        print(f"\n=== Testing GPU {gpu_id} ===")
        device = torch.device(f'cuda:{gpu_id}')
        
        # 基础计算测试
        start_time = time.time()
        x = torch.randn(1000, 1000, device=device)
        y = torch.randn(1000, 1000, device=device)
        for _ in range(100):
            z = torch.mm(x, y)
        torch.cuda.synchronize(device)
        compute_time = time.time() - start_time
        print(f"GPU {gpu_id} compute time: {compute_time:.3f}s")
        
        # 内存带宽测试
        start_time = time.time()
        large_tensor = torch.randn(10000, 10000, device=device)
        copied_tensor = large_tensor.clone()
        torch.cuda.synchronize(device)
        memory_time = time.time() - start_time
        print(f"GPU {gpu_id} memory time: {memory_time:.3f}s")

def test_gpu_communication():
    """测试GPU间通信"""
    print("\n=== Testing GPU Communication ===")
    
    # 测试1-2通信
    device1 = torch.device('cuda:1')
    device2 = torch.device('cuda:2')
    
    x = torch.randn(1000, 1000, device=device1)
    start_time = time.time()
    for _ in range(10):
        y = x.to(device2)
        z = y.to(device1)
    torch.cuda.synchronize()
    comm_time = time.time() - start_time
    print(f"GPU 1<->2 communication time: {comm_time:.3f}s")

if __name__ == "__main__":
    test_gpu_performance()
    test_gpu_communication()