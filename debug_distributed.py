import torch
import torch.distributed as dist
import torch.multiprocessing as mp
import os
import time

def test_distributed_performance(rank, world_size, gpus):
    """测试分布式训练性能"""
    os.environ['MASTER_ADDR'] = 'localhost'
    os.environ['MASTER_PORT'] = str(12355 + world_size)  # 避免端口冲突
    os.environ['NCCL_DEBUG'] = 'INFO'
    
    dist.init_process_group("nccl", rank=rank, world_size=world_size)
    torch.cuda.set_device(gpus[rank])
    device = torch.device(f'cuda:{gpus[rank]}')
    
    # 模拟训练batch
    model = torch.nn.Linear(1000, 1000).to(device)
    optimizer = torch.optim.Adam(model.parameters())
    
    # 预热
    for _ in range(5):
        x = torch.randn(512, 1000, device=device)
        y = model(x)
        loss = y.sum()
        loss.backward()
        
        # 关键：梯度同步
        for param in model.parameters():
            dist.all_reduce(param.grad.data, op=dist.ReduceOp.SUM)
            param.grad.data /= world_size
        
        optimizer.step()
        optimizer.zero_grad()
    
    # 正式测试
    torch.cuda.synchronize()
    start_time = time.time()
    
    for i in range(20):
        batch_start = time.time()
        
        x = torch.randn(512, 1000, device=device)
        y = model(x)
        loss = y.sum()
        loss.backward()
        
        # 梯度同步 - 这里可能是瓶颈
        sync_start = time.time()
        for param in model.parameters():
            dist.all_reduce(param.grad.data, op=dist.ReduceOp.SUM)
            param.grad.data /= world_size
        sync_time = time.time() - sync_start
        
        optimizer.step()
        optimizer.zero_grad()
        
        batch_time = time.time() - batch_start
        
        if rank == 0:
            print(f"Batch {i}: total={batch_time:.3f}s, sync={sync_time:.3f}s")
    
    total_time = time.time() - start_time
    if rank == 0:
        print(f"GPUs {gpus}: Average batch time: {total_time/20:.3f}s")
    
    dist.destroy_process_group()

def run_test(gpus):
    print(f"\n=== Testing GPUs: {gpus} ===")
    try:
        mp.spawn(test_distributed_performance, args=(len(gpus), gpus), nprocs=len(gpus))
    except Exception as e:
        print(f"Error with GPUs {gpus}: {e}")

if __name__ == "__main__":
    # 测试不同组合
    run_test([1, 2])      # 应该正常
    run_test([1, 3])      # 应该正常  
    run_test([1, 2, 3])   # 应该很慢
    run_test([2, 3])      # 新测试：看看是否是GPU 1的问题

"""
(prm) (base) surui@machine1:~/BoostingVLA$ /mnt/disk2/surui/miniconda3/envs/prm/bin/python /mnt/disk2/surui/BoostingVLA/debug_distributed.py

=== Testing GPUs: [1, 2] ===
machine1:3161103:3161103 [1] NCCL INFO Bootstrap : Using bond0:10.0.1.3<0>
machine1:3161103:3161103 [1] NCCL INFO NET/Plugin : dlerror=libnccl-net.so: cannot open shared object file: No such file or directory No plugin found (libnccl-net.so), using internal implementation
machine1:3161103:3161103 [1] NCCL INFO cudaDriverVersion 12040
NCCL version 2.19.3+cuda12.3
machine1:3161104:3161104 [2] NCCL INFO cudaDriverVersion 12040
machine1:3161104:3161104 [2] NCCL INFO Bootstrap : Using bond0:10.0.1.3<0>
machine1:3161104:3161104 [2] NCCL INFO NET/Plugin : dlerror=libnccl-net.so: cannot open shared object file: No such file or directory No plugin found (libnccl-net.so), using internal implementation
machine1:3161103:3161329 [1] NCCL INFO Failed to open libibverbs.so[.1]
machine1:3161103:3161329 [1] NCCL INFO NET/Socket : Using [0]bond0:10.0.1.3<0> [1]mlx5_ib8:100.96.1.37<0> [2]br-50bfd479b9ba:172.18.0.1<0> [3]br-46cbd64e257e:172.19.0.1<0> [4]br-4b73fd77a4e2:172.20.0.1<0> [5]br-af231856873e:172.21.0.1<0> [6]veth3da8205:fe80::90a1:70ff:fe01:a48a%veth3da8205<0> [7]veth95142d3:fe80::18e4:c2ff:fe4f:39a2%veth95142d3<0> [8]veth16999c4:fe80::38b4:bff:fecd:2ff2%veth16999c4<0> [9]vethd4fe970:fe80::88a6:6bff:fe12:d38c%vethd4fe970<0> [10]veth50d667b:fe80::34ac:36ff:fecb:c008%veth50d667b<0> [11]veth8b67a80:fe80::88b8:55ff:fe38:1741%veth8b67a80<0> [12]vethc492176:fe80::90fe:b1ff:feab:4c6b%vethc492176<0> [13]veth4a62d7c:fe80::e0b9:2ff:fef8:477%veth4a62d7c<0> [14]veth4d793c5:fe80::1cd3:67ff:feb5:ea%veth4d793c5<0> [15]veth70f9c41:fe80::1820:73ff:fe68:53fe%veth70f9c41<0>
machine1:3161103:3161329 [1] NCCL INFO Using non-device net plugin version 0
machine1:3161103:3161329 [1] NCCL INFO Using network Socket
machine1:3161104:3161338 [2] NCCL INFO Failed to open libibverbs.so[.1]
machine1:3161104:3161338 [2] NCCL INFO NET/Socket : Using [0]bond0:10.0.1.3<0> [1]mlx5_ib8:100.96.1.37<0> [2]br-50bfd479b9ba:172.18.0.1<0> [3]br-46cbd64e257e:172.19.0.1<0> [4]br-4b73fd77a4e2:172.20.0.1<0> [5]br-af231856873e:172.21.0.1<0> [6]veth3da8205:fe80::90a1:70ff:fe01:a48a%veth3da8205<0> [7]veth95142d3:fe80::18e4:c2ff:fe4f:39a2%veth95142d3<0> [8]veth16999c4:fe80::38b4:bff:fecd:2ff2%veth16999c4<0> [9]vethd4fe970:fe80::88a6:6bff:fe12:d38c%vethd4fe970<0> [10]veth50d667b:fe80::34ac:36ff:fecb:c008%veth50d667b<0> [11]veth8b67a80:fe80::88b8:55ff:fe38:1741%veth8b67a80<0> [12]vethc492176:fe80::90fe:b1ff:feab:4c6b%vethc492176<0> [13]veth4a62d7c:fe80::e0b9:2ff:fef8:477%veth4a62d7c<0> [14]veth4d793c5:fe80::1cd3:67ff:feb5:ea%veth4d793c5<0> [15]veth70f9c41:fe80::1820:73ff:fe68:53fe%veth70f9c41<0>
machine1:3161104:3161338 [2] NCCL INFO Using non-device net plugin version 0
machine1:3161104:3161338 [2] NCCL INFO Using network Socket
machine1:3161104:3161338 [2] NCCL INFO comm 0x9b03570 rank 1 nranks 2 cudaDev 2 nvmlDev 2 busId 4b000 commId 0x9876229cce975bb3 - Init START
machine1:3161103:3161329 [1] NCCL INFO comm 0xaea4740 rank 0 nranks 2 cudaDev 1 nvmlDev 1 busId 3a000 commId 0x9876229cce975bb3 - Init START
machine1:3161104:3161338 [2] NCCL INFO Setting affinity for GPU 2 to ff,ffffffff,ffff0000,00000000,00ffffff,ffffffff
machine1:3161103:3161329 [1] NCCL INFO Setting affinity for GPU 1 to ff,ffffffff,ffff0000,00000000,00ffffff,ffffffff
machine1:3161103:3161329 [1] NCCL INFO Channel 00/16 :    0   1
machine1:3161103:3161329 [1] NCCL INFO Channel 01/16 :    0   1
machine1:3161103:3161329 [1] NCCL INFO Channel 02/16 :    0   1
machine1:3161103:3161329 [1] NCCL INFO Channel 03/16 :    0   1
machine1:3161103:3161329 [1] NCCL INFO Channel 04/16 :    0   1
machine1:3161103:3161329 [1] NCCL INFO Channel 05/16 :    0   1
machine1:3161103:3161329 [1] NCCL INFO Channel 06/16 :    0   1
machine1:3161104:3161338 [2] NCCL INFO Trees [0] -1/-1/-1->1->0 [1] -1/-1/-1->1->0 [2] -1/-1/-1->1->0 [3] -1/-1/-1->1->0 [4] 0/-1/-1->1->-1 [5] 0/-1/-1->1->-1 [6] 0/-1/-1->1->-1 [7] 0/-1/-1->1->-1 [8] -1/-1/-1->1->0 [9] -1/-1/-1->1->0 [10] -1/-1/-1->1->0 [11] -1/-1/-1->1->0 [12] 0/-1/-1->1->-1 [13] 0/-1/-1->1->-1 [14] 0/-1/-1->1->-1 [15] 0/-1/-1->1->-1
machine1:3161103:3161329 [1] NCCL INFO Channel 07/16 :    0   1
machine1:3161103:3161329 [1] NCCL INFO Channel 08/16 :    0   1
machine1:3161104:3161338 [2] NCCL INFO P2P Chunksize set to 524288
machine1:3161103:3161329 [1] NCCL INFO Channel 09/16 :    0   1
machine1:3161103:3161329 [1] NCCL INFO Channel 10/16 :    0   1
machine1:3161103:3161329 [1] NCCL INFO Channel 11/16 :    0   1
machine1:3161103:3161329 [1] NCCL INFO Channel 12/16 :    0   1
machine1:3161103:3161329 [1] NCCL INFO Channel 13/16 :    0   1
machine1:3161103:3161329 [1] NCCL INFO Channel 14/16 :    0   1
machine1:3161103:3161329 [1] NCCL INFO Channel 15/16 :    0   1
machine1:3161103:3161329 [1] NCCL INFO Trees [0] 1/-1/-1->0->-1 [1] 1/-1/-1->0->-1 [2] 1/-1/-1->0->-1 [3] 1/-1/-1->0->-1 [4] -1/-1/-1->0->1 [5] -1/-1/-1->0->1 [6] -1/-1/-1->0->1 [7] -1/-1/-1->0->1 [8] 1/-1/-1->0->-1 [9] 1/-1/-1->0->-1 [10] 1/-1/-1->0->-1 [11] 1/-1/-1->0->-1 [12] -1/-1/-1->0->1 [13] -1/-1/-1->0->1 [14] -1/-1/-1->0->1 [15] -1/-1/-1->0->1
machine1:3161103:3161329 [1] NCCL INFO P2P Chunksize set to 524288
machine1:3161103:3161329 [1] NCCL INFO Channel 00/0 : 0[1] -> 1[2] via P2P/CUMEM
machine1:3161103:3161329 [1] NCCL INFO Channel 01/0 : 0[1] -> 1[2] via P2P/CUMEM
machine1:3161103:3161329 [1] NCCL INFO Channel 02/0 : 0[1] -> 1[2] via P2P/CUMEM
machine1:3161103:3161329 [1] NCCL INFO Channel 03/0 : 0[1] -> 1[2] via P2P/CUMEM
machine1:3161103:3161329 [1] NCCL INFO Channel 04/0 : 0[1] -> 1[2] via P2P/CUMEM
machine1:3161103:3161329 [1] NCCL INFO Channel 05/0 : 0[1] -> 1[2] via P2P/CUMEM
machine1:3161103:3161329 [1] NCCL INFO Channel 06/0 : 0[1] -> 1[2] via P2P/CUMEM
machine1:3161103:3161329 [1] NCCL INFO Channel 07/0 : 0[1] -> 1[2] via P2P/CUMEM
machine1:3161103:3161329 [1] NCCL INFO Channel 08/0 : 0[1] -> 1[2] via P2P/CUMEM
machine1:3161103:3161329 [1] NCCL INFO Channel 09/0 : 0[1] -> 1[2] via P2P/CUMEM
machine1:3161103:3161329 [1] NCCL INFO Channel 10/0 : 0[1] -> 1[2] via P2P/CUMEM
machine1:3161103:3161329 [1] NCCL INFO Channel 11/0 : 0[1] -> 1[2] via P2P/CUMEM
machine1:3161103:3161329 [1] NCCL INFO Channel 12/0 : 0[1] -> 1[2] via P2P/CUMEM
machine1:3161103:3161329 [1] NCCL INFO Channel 13/0 : 0[1] -> 1[2] via P2P/CUMEM
machine1:3161103:3161329 [1] NCCL INFO Channel 14/0 : 0[1] -> 1[2] via P2P/CUMEM
machine1:3161103:3161329 [1] NCCL INFO Channel 15/0 : 0[1] -> 1[2] via P2P/CUMEM
machine1:3161104:3161338 [2] NCCL INFO Channel 00/0 : 1[2] -> 0[1] via P2P/CUMEM
machine1:3161104:3161338 [2] NCCL INFO Channel 01/0 : 1[2] -> 0[1] via P2P/CUMEM
machine1:3161104:3161338 [2] NCCL INFO Channel 02/0 : 1[2] -> 0[1] via P2P/CUMEM
machine1:3161104:3161338 [2] NCCL INFO Channel 03/0 : 1[2] -> 0[1] via P2P/CUMEM
machine1:3161104:3161338 [2] NCCL INFO Channel 04/0 : 1[2] -> 0[1] via P2P/CUMEM
machine1:3161104:3161338 [2] NCCL INFO Channel 05/0 : 1[2] -> 0[1] via P2P/CUMEM
machine1:3161104:3161338 [2] NCCL INFO Channel 06/0 : 1[2] -> 0[1] via P2P/CUMEM
machine1:3161104:3161338 [2] NCCL INFO Channel 07/0 : 1[2] -> 0[1] via P2P/CUMEM
machine1:3161104:3161338 [2] NCCL INFO Channel 08/0 : 1[2] -> 0[1] via P2P/CUMEM
machine1:3161104:3161338 [2] NCCL INFO Channel 09/0 : 1[2] -> 0[1] via P2P/CUMEM
machine1:3161104:3161338 [2] NCCL INFO Channel 10/0 : 1[2] -> 0[1] via P2P/CUMEM
machine1:3161104:3161338 [2] NCCL INFO Channel 11/0 : 1[2] -> 0[1] via P2P/CUMEM
machine1:3161104:3161338 [2] NCCL INFO Channel 12/0 : 1[2] -> 0[1] via P2P/CUMEM
machine1:3161104:3161338 [2] NCCL INFO Channel 13/0 : 1[2] -> 0[1] via P2P/CUMEM
machine1:3161104:3161338 [2] NCCL INFO Channel 14/0 : 1[2] -> 0[1] via P2P/CUMEM
machine1:3161104:3161338 [2] NCCL INFO Channel 15/0 : 1[2] -> 0[1] via P2P/CUMEM
machine1:3161104:3161338 [2] NCCL INFO Connected all rings
machine1:3161104:3161338 [2] NCCL INFO Connected all trees
machine1:3161104:3161338 [2] NCCL INFO threadThresholds 8/8/64 | 16/8/64 | 512 | 512
machine1:3161104:3161338 [2] NCCL INFO 16 coll channels, 0 nvls channels, 16 p2p channels, 16 p2p channels per peer
machine1:3161103:3161329 [1] NCCL INFO Connected all rings
machine1:3161103:3161329 [1] NCCL INFO Connected all trees
machine1:3161103:3161329 [1] NCCL INFO threadThresholds 8/8/64 | 16/8/64 | 512 | 512
machine1:3161103:3161329 [1] NCCL INFO 16 coll channels, 0 nvls channels, 16 p2p channels, 16 p2p channels per peer
machine1:3161103:3161329 [1] NCCL INFO comm 0xaea4740 rank 0 nranks 2 cudaDev 1 nvmlDev 1 busId 3a000 commId 0x9876229cce975bb3 - Init COMPLETE
machine1:3161104:3161338 [2] NCCL INFO comm 0x9b03570 rank 1 nranks 2 cudaDev 2 nvmlDev 2 busId 4b000 commId 0x9876229cce975bb3 - Init COMPLETE
Batch 0: total=0.001s, sync=0.000s
Batch 1: total=0.001s, sync=0.000s
Batch 2: total=0.001s, sync=0.000s
Batch 3: total=0.001s, sync=0.000s
Batch 4: total=0.001s, sync=0.000s
Batch 5: total=0.001s, sync=0.000s
Batch 6: total=0.001s, sync=0.000s
Batch 7: total=0.001s, sync=0.000s
Batch 8: total=0.001s, sync=0.000s
Batch 9: total=0.001s, sync=0.000s
Batch 10: total=0.001s, sync=0.000s
Batch 11: total=0.001s, sync=0.000s
Batch 12: total=0.001s, sync=0.000s
Batch 13: total=0.001s, sync=0.000s
Batch 14: total=0.001s, sync=0.000s
Batch 15: total=0.001s, sync=0.000s
Batch 16: total=0.001s, sync=0.000s
Batch 17: total=0.001s, sync=0.000s
Batch 18: total=0.001s, sync=0.000s
Batch 19: total=0.001s, sync=0.000s
GPUs [1, 2]: Average batch time: 0.001s
machine1:3161103:3161359 [1] NCCL INFO [Service thread] Connection closed by localRank 1
machine1:3161104:3161360 [2] NCCL INFO [Service thread] Connection closed by localRank 1
machine1:3161103:3161359 [1] NCCL INFO [Service thread] Connection closed by localRank 0
machine1:3161104:3161360 [2] NCCL INFO [Service thread] Connection closed by localRank 0
machine1:3161103:3161103 [1] NCCL INFO comm 0xaea4740 rank 0 nranks 2 cudaDev 1 busId 3a000 - Abort COMPLETE
machine1:3161104:3161104 [2] NCCL INFO comm 0x9b03570 rank 1 nranks 2 cudaDev 2 busId 4b000 - Abort COMPLETE

=== Testing GPUs: [1, 3] ===
machine1:3161413:3161413 [1] NCCL INFO Bootstrap : Using bond0:10.0.1.3<0>
machine1:3161413:3161413 [1] NCCL INFO NET/Plugin : dlerror=libnccl-net.so: cannot open shared object file: No such file or directory No plugin found (libnccl-net.so), using internal implementation
machine1:3161413:3161413 [1] NCCL INFO cudaDriverVersion 12040
NCCL version 2.19.3+cuda12.3
machine1:3161414:3161414 [3] NCCL INFO cudaDriverVersion 12040
machine1:3161414:3161414 [3] NCCL INFO Bootstrap : Using bond0:10.0.1.3<0>
machine1:3161414:3161414 [3] NCCL INFO NET/Plugin : dlerror=libnccl-net.so: cannot open shared object file: No such file or directory No plugin found (libnccl-net.so), using internal implementation
machine1:3161413:3161625 [1] NCCL INFO Failed to open libibverbs.so[.1]
machine1:3161413:3161625 [1] NCCL INFO NET/Socket : Using [0]bond0:10.0.1.3<0> [1]mlx5_ib8:100.96.1.37<0> [2]br-50bfd479b9ba:172.18.0.1<0> [3]br-46cbd64e257e:172.19.0.1<0> [4]br-4b73fd77a4e2:172.20.0.1<0> [5]br-af231856873e:172.21.0.1<0> [6]veth3da8205:fe80::90a1:70ff:fe01:a48a%veth3da8205<0> [7]veth95142d3:fe80::18e4:c2ff:fe4f:39a2%veth95142d3<0> [8]veth16999c4:fe80::38b4:bff:fecd:2ff2%veth16999c4<0> [9]vethd4fe970:fe80::88a6:6bff:fe12:d38c%vethd4fe970<0> [10]veth50d667b:fe80::34ac:36ff:fecb:c008%veth50d667b<0> [11]veth8b67a80:fe80::88b8:55ff:fe38:1741%veth8b67a80<0> [12]vethc492176:fe80::90fe:b1ff:feab:4c6b%vethc492176<0> [13]veth4a62d7c:fe80::e0b9:2ff:fef8:477%veth4a62d7c<0> [14]veth4d793c5:fe80::1cd3:67ff:feb5:ea%veth4d793c5<0> [15]veth70f9c41:fe80::1820:73ff:fe68:53fe%veth70f9c41<0>
machine1:3161413:3161625 [1] NCCL INFO Using non-device net plugin version 0
machine1:3161413:3161625 [1] NCCL INFO Using network Socket
machine1:3161414:3161626 [3] NCCL INFO Failed to open libibverbs.so[.1]
machine1:3161414:3161626 [3] NCCL INFO NET/Socket : Using [0]bond0:10.0.1.3<0> [1]mlx5_ib8:100.96.1.37<0> [2]br-50bfd479b9ba:172.18.0.1<0> [3]br-46cbd64e257e:172.19.0.1<0> [4]br-4b73fd77a4e2:172.20.0.1<0> [5]br-af231856873e:172.21.0.1<0> [6]veth3da8205:fe80::90a1:70ff:fe01:a48a%veth3da8205<0> [7]veth95142d3:fe80::18e4:c2ff:fe4f:39a2%veth95142d3<0> [8]veth16999c4:fe80::38b4:bff:fecd:2ff2%veth16999c4<0> [9]vethd4fe970:fe80::88a6:6bff:fe12:d38c%vethd4fe970<0> [10]veth50d667b:fe80::34ac:36ff:fecb:c008%veth50d667b<0> [11]veth8b67a80:fe80::88b8:55ff:fe38:1741%veth8b67a80<0> [12]vethc492176:fe80::90fe:b1ff:feab:4c6b%vethc492176<0> [13]veth4a62d7c:fe80::e0b9:2ff:fef8:477%veth4a62d7c<0> [14]veth4d793c5:fe80::1cd3:67ff:feb5:ea%veth4d793c5<0> [15]veth70f9c41:fe80::1820:73ff:fe68:53fe%veth70f9c41<0>
machine1:3161414:3161626 [3] NCCL INFO Using non-device net plugin version 0
machine1:3161414:3161626 [3] NCCL INFO Using network Socket
machine1:3161414:3161626 [3] NCCL INFO comm 0xa6550e0 rank 1 nranks 2 cudaDev 3 nvmlDev 3 busId 5c000 commId 0x7a83b6d028be7b4d - Init START
machine1:3161413:3161625 [1] NCCL INFO comm 0xa1e3750 rank 0 nranks 2 cudaDev 1 nvmlDev 1 busId 3a000 commId 0x7a83b6d028be7b4d - Init START
machine1:3161413:3161625 [1] NCCL INFO Setting affinity for GPU 1 to ff,ffffffff,ffff0000,00000000,00ffffff,ffffffff
machine1:3161414:3161626 [3] NCCL INFO Setting affinity for GPU 3 to ff,ffffffff,ffff0000,00000000,00ffffff,ffffffff
machine1:3161413:3161625 [1] NCCL INFO Channel 00/16 :    0   1
machine1:3161413:3161625 [1] NCCL INFO Channel 01/16 :    0   1
machine1:3161413:3161625 [1] NCCL INFO Channel 02/16 :    0   1
machine1:3161413:3161625 [1] NCCL INFO Channel 03/16 :    0   1
machine1:3161413:3161625 [1] NCCL INFO Channel 04/16 :    0   1
machine1:3161413:3161625 [1] NCCL INFO Channel 05/16 :    0   1
machine1:3161413:3161625 [1] NCCL INFO Channel 06/16 :    0   1
machine1:3161414:3161626 [3] NCCL INFO Trees [0] -1/-1/-1->1->0 [1] -1/-1/-1->1->0 [2] -1/-1/-1->1->0 [3] -1/-1/-1->1->0 [4] 0/-1/-1->1->-1 [5] 0/-1/-1->1->-1 [6] 0/-1/-1->1->-1 [7] 0/-1/-1->1->-1 [8] -1/-1/-1->1->0 [9] -1/-1/-1->1->0 [10] -1/-1/-1->1->0 [11] -1/-1/-1->1->0 [12] 0/-1/-1->1->-1 [13] 0/-1/-1->1->-1 [14] 0/-1/-1->1->-1 [15] 0/-1/-1->1->-1
machine1:3161413:3161625 [1] NCCL INFO Channel 07/16 :    0   1
machine1:3161414:3161626 [3] NCCL INFO P2P Chunksize set to 524288
machine1:3161413:3161625 [1] NCCL INFO Channel 08/16 :    0   1
machine1:3161413:3161625 [1] NCCL INFO Channel 09/16 :    0   1
machine1:3161413:3161625 [1] NCCL INFO Channel 10/16 :    0   1
machine1:3161413:3161625 [1] NCCL INFO Channel 11/16 :    0   1
machine1:3161413:3161625 [1] NCCL INFO Channel 12/16 :    0   1
machine1:3161413:3161625 [1] NCCL INFO Channel 13/16 :    0   1
machine1:3161413:3161625 [1] NCCL INFO Channel 14/16 :    0   1
machine1:3161413:3161625 [1] NCCL INFO Channel 15/16 :    0   1
machine1:3161413:3161625 [1] NCCL INFO Trees [0] 1/-1/-1->0->-1 [1] 1/-1/-1->0->-1 [2] 1/-1/-1->0->-1 [3] 1/-1/-1->0->-1 [4] -1/-1/-1->0->1 [5] -1/-1/-1->0->1 [6] -1/-1/-1->0->1 [7] -1/-1/-1->0->1 [8] 1/-1/-1->0->-1 [9] 1/-1/-1->0->-1 [10] 1/-1/-1->0->-1 [11] 1/-1/-1->0->-1 [12] -1/-1/-1->0->1 [13] -1/-1/-1->0->1 [14] -1/-1/-1->0->1 [15] -1/-1/-1->0->1
machine1:3161413:3161625 [1] NCCL INFO P2P Chunksize set to 524288
machine1:3161413:3161625 [1] NCCL INFO Channel 00/0 : 0[1] -> 1[3] via P2P/CUMEM
machine1:3161413:3161625 [1] NCCL INFO Channel 01/0 : 0[1] -> 1[3] via P2P/CUMEM
machine1:3161413:3161625 [1] NCCL INFO Channel 02/0 : 0[1] -> 1[3] via P2P/CUMEM
machine1:3161413:3161625 [1] NCCL INFO Channel 03/0 : 0[1] -> 1[3] via P2P/CUMEM
machine1:3161413:3161625 [1] NCCL INFO Channel 04/0 : 0[1] -> 1[3] via P2P/CUMEM
machine1:3161413:3161625 [1] NCCL INFO Channel 05/0 : 0[1] -> 1[3] via P2P/CUMEM
machine1:3161413:3161625 [1] NCCL INFO Channel 06/0 : 0[1] -> 1[3] via P2P/CUMEM
machine1:3161413:3161625 [1] NCCL INFO Channel 07/0 : 0[1] -> 1[3] via P2P/CUMEM
machine1:3161413:3161625 [1] NCCL INFO Channel 08/0 : 0[1] -> 1[3] via P2P/CUMEM
machine1:3161413:3161625 [1] NCCL INFO Channel 09/0 : 0[1] -> 1[3] via P2P/CUMEM
machine1:3161413:3161625 [1] NCCL INFO Channel 10/0 : 0[1] -> 1[3] via P2P/CUMEM
machine1:3161413:3161625 [1] NCCL INFO Channel 11/0 : 0[1] -> 1[3] via P2P/CUMEM
machine1:3161413:3161625 [1] NCCL INFO Channel 12/0 : 0[1] -> 1[3] via P2P/CUMEM
machine1:3161413:3161625 [1] NCCL INFO Channel 13/0 : 0[1] -> 1[3] via P2P/CUMEM
machine1:3161413:3161625 [1] NCCL INFO Channel 14/0 : 0[1] -> 1[3] via P2P/CUMEM
machine1:3161413:3161625 [1] NCCL INFO Channel 15/0 : 0[1] -> 1[3] via P2P/CUMEM
machine1:3161414:3161626 [3] NCCL INFO Channel 00/0 : 1[3] -> 0[1] via P2P/CUMEM
machine1:3161414:3161626 [3] NCCL INFO Channel 01/0 : 1[3] -> 0[1] via P2P/CUMEM
machine1:3161414:3161626 [3] NCCL INFO Channel 02/0 : 1[3] -> 0[1] via P2P/CUMEM
machine1:3161414:3161626 [3] NCCL INFO Channel 03/0 : 1[3] -> 0[1] via P2P/CUMEM
machine1:3161414:3161626 [3] NCCL INFO Channel 04/0 : 1[3] -> 0[1] via P2P/CUMEM
machine1:3161414:3161626 [3] NCCL INFO Channel 05/0 : 1[3] -> 0[1] via P2P/CUMEM
machine1:3161414:3161626 [3] NCCL INFO Channel 06/0 : 1[3] -> 0[1] via P2P/CUMEM
machine1:3161414:3161626 [3] NCCL INFO Channel 07/0 : 1[3] -> 0[1] via P2P/CUMEM
machine1:3161414:3161626 [3] NCCL INFO Channel 08/0 : 1[3] -> 0[1] via P2P/CUMEM
machine1:3161414:3161626 [3] NCCL INFO Channel 09/0 : 1[3] -> 0[1] via P2P/CUMEM
machine1:3161414:3161626 [3] NCCL INFO Channel 10/0 : 1[3] -> 0[1] via P2P/CUMEM
machine1:3161414:3161626 [3] NCCL INFO Channel 11/0 : 1[3] -> 0[1] via P2P/CUMEM
machine1:3161414:3161626 [3] NCCL INFO Channel 12/0 : 1[3] -> 0[1] via P2P/CUMEM
machine1:3161414:3161626 [3] NCCL INFO Channel 13/0 : 1[3] -> 0[1] via P2P/CUMEM
machine1:3161414:3161626 [3] NCCL INFO Channel 14/0 : 1[3] -> 0[1] via P2P/CUMEM
machine1:3161414:3161626 [3] NCCL INFO Channel 15/0 : 1[3] -> 0[1] via P2P/CUMEM
machine1:3161414:3161626 [3] NCCL INFO Connected all rings
machine1:3161414:3161626 [3] NCCL INFO Connected all trees
machine1:3161413:3161625 [1] NCCL INFO Connected all rings
machine1:3161413:3161625 [1] NCCL INFO Connected all trees
machine1:3161414:3161626 [3] NCCL INFO threadThresholds 8/8/64 | 16/8/64 | 512 | 512
machine1:3161414:3161626 [3] NCCL INFO 16 coll channels, 0 nvls channels, 16 p2p channels, 16 p2p channels per peer
machine1:3161413:3161625 [1] NCCL INFO threadThresholds 8/8/64 | 16/8/64 | 512 | 512
machine1:3161413:3161625 [1] NCCL INFO 16 coll channels, 0 nvls channels, 16 p2p channels, 16 p2p channels per peer
machine1:3161414:3161626 [3] NCCL INFO comm 0xa6550e0 rank 1 nranks 2 cudaDev 3 nvmlDev 3 busId 5c000 commId 0x7a83b6d028be7b4d - Init COMPLETE
machine1:3161413:3161625 [1] NCCL INFO comm 0xa1e3750 rank 0 nranks 2 cudaDev 1 nvmlDev 1 busId 3a000 commId 0x7a83b6d028be7b4d - Init COMPLETE
Batch 0: total=0.001s, sync=0.000s
Batch 1: total=0.001s, sync=0.000s
Batch 2: total=0.001s, sync=0.000s
Batch 3: total=0.001s, sync=0.000s
Batch 4: total=0.001s, sync=0.000s
Batch 5: total=0.001s, sync=0.000s
Batch 6: total=0.001s, sync=0.000s
Batch 7: total=0.001s, sync=0.000s
Batch 8: total=0.001s, sync=0.000s
Batch 9: total=0.001s, sync=0.000s
Batch 10: total=0.001s, sync=0.000s
Batch 11: total=0.001s, sync=0.000s
Batch 12: total=0.001s, sync=0.000s
Batch 13: total=0.001s, sync=0.000s
Batch 14: total=0.001s, sync=0.000s
Batch 15: total=0.001s, sync=0.000s
Batch 16: total=0.001s, sync=0.000s
Batch 17: total=0.001s, sync=0.000s
Batch 18: total=0.001s, sync=0.000s
Batch 19: total=0.001s, sync=0.000s
GPUs [1, 3]: Average batch time: 0.001s
machine1:3161413:3161654 [1] NCCL INFO [Service thread] Connection closed by localRank 1
machine1:3161414:3161653 [3] NCCL INFO [Service thread] Connection closed by localRank 1
machine1:3161413:3161654 [1] NCCL INFO [Service thread] Connection closed by localRank 0
machine1:3161414:3161653 [3] NCCL INFO [Service thread] Connection closed by localRank 0
machine1:3161413:3161413 [1] NCCL INFO comm 0xa1e3750 rank 0 nranks 2 cudaDev 1 busId 3a000 - Abort COMPLETE
machine1:3161414:3161414 [3] NCCL INFO comm 0xa6550e0 rank 1 nranks 2 cudaDev 3 busId 5c000 - Abort COMPLETE

=== Testing GPUs: [1, 2, 3] ===
machine1:3161697:3161697 [1] NCCL INFO Bootstrap : Using bond0:10.0.1.3<0>
machine1:3161697:3161697 [1] NCCL INFO NET/Plugin : dlerror=libnccl-net.so: cannot open shared object file: No such file or directory No plugin found (libnccl-net.so), using internal implementation
machine1:3161697:3161697 [1] NCCL INFO cudaDriverVersion 12040
NCCL version 2.19.3+cuda12.3
machine1:3161698:3161698 [2] NCCL INFO cudaDriverVersion 12040
machine1:3161698:3161698 [2] NCCL INFO Bootstrap : Using bond0:10.0.1.3<0>
machine1:3161698:3161698 [2] NCCL INFO NET/Plugin : dlerror=libnccl-net.so: cannot open shared object file: No such file or directory No plugin found (libnccl-net.so), using internal implementation
machine1:3161699:3161699 [3] NCCL INFO cudaDriverVersion 12040
machine1:3161699:3161699 [3] NCCL INFO Bootstrap : Using bond0:10.0.1.3<0>
machine1:3161699:3161699 [3] NCCL INFO NET/Plugin : dlerror=libnccl-net.so: cannot open shared object file: No such file or directory No plugin found (libnccl-net.so), using internal implementation
machine1:3161697:3161993 [1] NCCL INFO Failed to open libibverbs.so[.1]
machine1:3161697:3161993 [1] NCCL INFO NET/Socket : Using [0]bond0:10.0.1.3<0> [1]mlx5_ib8:100.96.1.37<0> [2]br-50bfd479b9ba:172.18.0.1<0> [3]br-46cbd64e257e:172.19.0.1<0> [4]br-4b73fd77a4e2:172.20.0.1<0> [5]br-af231856873e:172.21.0.1<0> [6]veth3da8205:fe80::90a1:70ff:fe01:a48a%veth3da8205<0> [7]veth95142d3:fe80::18e4:c2ff:fe4f:39a2%veth95142d3<0> [8]veth16999c4:fe80::38b4:bff:fecd:2ff2%veth16999c4<0> [9]vethd4fe970:fe80::88a6:6bff:fe12:d38c%vethd4fe970<0> [10]veth50d667b:fe80::34ac:36ff:fecb:c008%veth50d667b<0> [11]veth8b67a80:fe80::88b8:55ff:fe38:1741%veth8b67a80<0> [12]vethc492176:fe80::90fe:b1ff:feab:4c6b%vethc492176<0> [13]veth4a62d7c:fe80::e0b9:2ff:fef8:477%veth4a62d7c<0> [14]veth4d793c5:fe80::1cd3:67ff:feb5:ea%veth4d793c5<0> [15]veth70f9c41:fe80::1820:73ff:fe68:53fe%veth70f9c41<0>
machine1:3161697:3161993 [1] NCCL INFO Using non-device net plugin version 0
machine1:3161697:3161993 [1] NCCL INFO Using network Socket
machine1:3161699:3162003 [3] NCCL INFO Failed to open libibverbs.so[.1]
machine1:3161699:3162003 [3] NCCL INFO NET/Socket : Using [0]bond0:10.0.1.3<0> [1]mlx5_ib8:100.96.1.37<0> [2]br-50bfd479b9ba:172.18.0.1<0> [3]br-46cbd64e257e:172.19.0.1<0> [4]br-4b73fd77a4e2:172.20.0.1<0> [5]br-af231856873e:172.21.0.1<0> [6]veth3da8205:fe80::90a1:70ff:fe01:a48a%veth3da8205<0> [7]veth95142d3:fe80::18e4:c2ff:fe4f:39a2%veth95142d3<0> [8]veth16999c4:fe80::38b4:bff:fecd:2ff2%veth16999c4<0> [9]vethd4fe970:fe80::88a6:6bff:fe12:d38c%vethd4fe970<0> [10]veth50d667b:fe80::34ac:36ff:fecb:c008%veth50d667b<0> [11]veth8b67a80:fe80::88b8:55ff:fe38:1741%veth8b67a80<0> [12]vethc492176:fe80::90fe:b1ff:feab:4c6b%vethc492176<0> [13]veth4a62d7c:fe80::e0b9:2ff:fef8:477%veth4a62d7c<0> [14]veth4d793c5:fe80::1cd3:67ff:feb5:ea%veth4d793c5<0> [15]veth70f9c41:fe80::1820:73ff:fe68:53fe%veth70f9c41<0>
machine1:3161699:3162003 [3] NCCL INFO Using non-device net plugin version 0
machine1:3161699:3162003 [3] NCCL INFO Using network Socket
machine1:3161698:3161994 [2] NCCL INFO Failed to open libibverbs.so[.1]
machine1:3161698:3161994 [2] NCCL INFO NET/Socket : Using [0]bond0:10.0.1.3<0> [1]mlx5_ib8:100.96.1.37<0> [2]br-50bfd479b9ba:172.18.0.1<0> [3]br-46cbd64e257e:172.19.0.1<0> [4]br-4b73fd77a4e2:172.20.0.1<0> [5]br-af231856873e:172.21.0.1<0> [6]veth3da8205:fe80::90a1:70ff:fe01:a48a%veth3da8205<0> [7]veth95142d3:fe80::18e4:c2ff:fe4f:39a2%veth95142d3<0> [8]veth16999c4:fe80::38b4:bff:fecd:2ff2%veth16999c4<0> [9]vethd4fe970:fe80::88a6:6bff:fe12:d38c%vethd4fe970<0> [10]veth50d667b:fe80::34ac:36ff:fecb:c008%veth50d667b<0> [11]veth8b67a80:fe80::88b8:55ff:fe38:1741%veth8b67a80<0> [12]vethc492176:fe80::90fe:b1ff:feab:4c6b%vethc492176<0> [13]veth4a62d7c:fe80::e0b9:2ff:fef8:477%veth4a62d7c<0> [14]veth4d793c5:fe80::1cd3:67ff:feb5:ea%veth4d793c5<0> [15]veth70f9c41:fe80::1820:73ff:fe68:53fe%veth70f9c41<0>
machine1:3161698:3161994 [2] NCCL INFO Using non-device net plugin version 0
machine1:3161698:3161994 [2] NCCL INFO Using network Socket
machine1:3161699:3162003 [3] NCCL INFO comm 0xacbbbc0 rank 2 nranks 3 cudaDev 3 nvmlDev 3 busId 5c000 commId 0xd70f0ec1db0d96b9 - Init START
machine1:3161698:3161994 [2] NCCL INFO comm 0xaf7e0e0 rank 1 nranks 3 cudaDev 2 nvmlDev 2 busId 4b000 commId 0xd70f0ec1db0d96b9 - Init START
machine1:3161697:3161993 [1] NCCL INFO comm 0x9f17170 rank 0 nranks 3 cudaDev 1 nvmlDev 1 busId 3a000 commId 0xd70f0ec1db0d96b9 - Init START
machine1:3161697:3161993 [1] NCCL INFO Setting affinity for GPU 1 to ff,ffffffff,ffff0000,00000000,00ffffff,ffffffff
machine1:3161697:3161993 [1] NCCL INFO NVLS multicast support is available on dev 1
machine1:3161698:3161994 [2] NCCL INFO Setting affinity for GPU 2 to ff,ffffffff,ffff0000,00000000,00ffffff,ffffffff
machine1:3161698:3161994 [2] NCCL INFO NVLS multicast support is available on dev 2
machine1:3161699:3162003 [3] NCCL INFO Setting affinity for GPU 3 to ff,ffffffff,ffff0000,00000000,00ffffff,ffffffff
machine1:3161699:3162003 [3] NCCL INFO NVLS multicast support is available on dev 3
machine1:3161698:3161994 [2] NCCL INFO Trees [0] 2/-1/-1->1->0 [1] 2/-1/-1->1->0 [2] 2/-1/-1->1->0 [3] 2/-1/-1->1->0 [4] 2/-1/-1->1->-1 [5] 2/-1/-1->1->-1 [6] -1/-1/-1->1->0 [7] -1/-1/-1->1->0 [8] 2/-1/-1->1->0 [9] 2/-1/-1->1->0 [10] 2/-1/-1->1->0 [11] 2/-1/-1->1->0 [12] 2/-1/-1->1->-1 [13] 2/-1/-1->1->-1 [14] -1/-1/-1->1->0 [15] -1/-1/-1->1->0
machine1:3161697:3161993 [1] NCCL INFO Channel 00/16 :    0   1   2
machine1:3161698:3161994 [2] NCCL INFO P2P Chunksize set to 524288
machine1:3161699:3162003 [3] NCCL INFO Trees [0] -1/-1/-1->2->1 [1] -1/-1/-1->2->1 [2] -1/-1/-1->2->1 [3] -1/-1/-1->2->1 [4] 0/-1/-1->2->1 [5] 0/-1/-1->2->1 [6] 0/-1/-1->2->-1 [7] 0/-1/-1->2->-1 [8] -1/-1/-1->2->1 [9] -1/-1/-1->2->1 [10] -1/-1/-1->2->1 [11] -1/-1/-1->2->1 [12] 0/-1/-1->2->1 [13] 0/-1/-1->2->1 [14] 0/-1/-1->2->-1 [15] 0/-1/-1->2->-1
machine1:3161697:3161993 [1] NCCL INFO Channel 01/16 :    0   1   2
machine1:3161699:3162003 [3] NCCL INFO P2P Chunksize set to 524288
machine1:3161697:3161993 [1] NCCL INFO Channel 02/16 :    0   1   2
machine1:3161697:3161993 [1] NCCL INFO Channel 03/16 :    0   1   2
machine1:3161697:3161993 [1] NCCL INFO Channel 04/16 :    0   1   2
machine1:3161697:3161993 [1] NCCL INFO Channel 05/16 :    0   1   2
machine1:3161697:3161993 [1] NCCL INFO Channel 06/16 :    0   1   2
machine1:3161697:3161993 [1] NCCL INFO Channel 07/16 :    0   1   2
machine1:3161697:3161993 [1] NCCL INFO Channel 08/16 :    0   1   2
machine1:3161697:3161993 [1] NCCL INFO Channel 09/16 :    0   1   2
machine1:3161697:3161993 [1] NCCL INFO Channel 10/16 :    0   1   2
machine1:3161697:3161993 [1] NCCL INFO Channel 11/16 :    0   1   2
machine1:3161697:3161993 [1] NCCL INFO Channel 12/16 :    0   1   2
machine1:3161697:3161993 [1] NCCL INFO Channel 13/16 :    0   1   2
machine1:3161697:3161993 [1] NCCL INFO Channel 14/16 :    0   1   2
machine1:3161697:3161993 [1] NCCL INFO Channel 15/16 :    0   1   2
machine1:3161697:3161993 [1] NCCL INFO Trees [0] 1/-1/-1->0->-1 [1] 1/-1/-1->0->-1 [2] 1/-1/-1->0->-1 [3] 1/-1/-1->0->-1 [4] -1/-1/-1->0->2 [5] -1/-1/-1->0->2 [6] 1/-1/-1->0->2 [7] 1/-1/-1->0->2 [8] 1/-1/-1->0->-1 [9] 1/-1/-1->0->-1 [10] 1/-1/-1->0->-1 [11] 1/-1/-1->0->-1 [12] -1/-1/-1->0->2 [13] -1/-1/-1->0->2 [14] 1/-1/-1->0->2 [15] 1/-1/-1->0->2
machine1:3161697:3161993 [1] NCCL INFO P2P Chunksize set to 524288
machine1:3161697:3161993 [1] NCCL INFO Channel 00/0 : 0[1] -> 1[2] via P2P/CUMEM
machine1:3161697:3161993 [1] NCCL INFO Channel 01/0 : 0[1] -> 1[2] via P2P/CUMEM
machine1:3161697:3161993 [1] NCCL INFO Channel 02/0 : 0[1] -> 1[2] via P2P/CUMEM
machine1:3161697:3161993 [1] NCCL INFO Channel 03/0 : 0[1] -> 1[2] via P2P/CUMEM
machine1:3161697:3161993 [1] NCCL INFO Channel 04/0 : 0[1] -> 1[2] via P2P/CUMEM
machine1:3161697:3161993 [1] NCCL INFO Channel 05/0 : 0[1] -> 1[2] via P2P/CUMEM
machine1:3161697:3161993 [1] NCCL INFO Channel 06/0 : 0[1] -> 1[2] via P2P/CUMEM
machine1:3161697:3161993 [1] NCCL INFO Channel 07/0 : 0[1] -> 1[2] via P2P/CUMEM
machine1:3161697:3161993 [1] NCCL INFO Channel 08/0 : 0[1] -> 1[2] via P2P/CUMEM
machine1:3161697:3161993 [1] NCCL INFO Channel 09/0 : 0[1] -> 1[2] via P2P/CUMEM
machine1:3161697:3161993 [1] NCCL INFO Channel 10/0 : 0[1] -> 1[2] via P2P/CUMEM
machine1:3161697:3161993 [1] NCCL INFO Channel 11/0 : 0[1] -> 1[2] via P2P/CUMEM
machine1:3161697:3161993 [1] NCCL INFO Channel 12/0 : 0[1] -> 1[2] via P2P/CUMEM
machine1:3161697:3161993 [1] NCCL INFO Channel 13/0 : 0[1] -> 1[2] via P2P/CUMEM
machine1:3161697:3161993 [1] NCCL INFO Channel 14/0 : 0[1] -> 1[2] via P2P/CUMEM
machine1:3161697:3161993 [1] NCCL INFO Channel 15/0 : 0[1] -> 1[2] via P2P/CUMEM
machine1:3161699:3162003 [3] NCCL INFO Channel 00/0 : 2[3] -> 0[1] via P2P/CUMEM
machine1:3161699:3162003 [3] NCCL INFO Channel 01/0 : 2[3] -> 0[1] via P2P/CUMEM
machine1:3161699:3162003 [3] NCCL INFO Channel 02/0 : 2[3] -> 0[1] via P2P/CUMEM
machine1:3161699:3162003 [3] NCCL INFO Channel 03/0 : 2[3] -> 0[1] via P2P/CUMEM
machine1:3161699:3162003 [3] NCCL INFO Channel 04/0 : 2[3] -> 0[1] via P2P/CUMEM
machine1:3161699:3162003 [3] NCCL INFO Channel 05/0 : 2[3] -> 0[1] via P2P/CUMEM
machine1:3161699:3162003 [3] NCCL INFO Channel 06/0 : 2[3] -> 0[1] via P2P/CUMEM
machine1:3161699:3162003 [3] NCCL INFO Channel 07/0 : 2[3] -> 0[1] via P2P/CUMEM
machine1:3161699:3162003 [3] NCCL INFO Channel 08/0 : 2[3] -> 0[1] via P2P/CUMEM
machine1:3161699:3162003 [3] NCCL INFO Channel 09/0 : 2[3] -> 0[1] via P2P/CUMEM
machine1:3161699:3162003 [3] NCCL INFO Channel 10/0 : 2[3] -> 0[1] via P2P/CUMEM
machine1:3161699:3162003 [3] NCCL INFO Channel 11/0 : 2[3] -> 0[1] via P2P/CUMEM
machine1:3161699:3162003 [3] NCCL INFO Channel 12/0 : 2[3] -> 0[1] via P2P/CUMEM
machine1:3161699:3162003 [3] NCCL INFO Channel 13/0 : 2[3] -> 0[1] via P2P/CUMEM
machine1:3161699:3162003 [3] NCCL INFO Channel 14/0 : 2[3] -> 0[1] via P2P/CUMEM
machine1:3161698:3161994 [2] NCCL INFO Channel 00/0 : 1[2] -> 2[3] via P2P/CUMEM
machine1:3161699:3162003 [3] NCCL INFO Channel 15/0 : 2[3] -> 0[1] via P2P/CUMEM
machine1:3161698:3161994 [2] NCCL INFO Channel 01/0 : 1[2] -> 2[3] via P2P/CUMEM
machine1:3161698:3161994 [2] NCCL INFO Channel 02/0 : 1[2] -> 2[3] via P2P/CUMEM
machine1:3161698:3161994 [2] NCCL INFO Channel 03/0 : 1[2] -> 2[3] via P2P/CUMEM
machine1:3161698:3161994 [2] NCCL INFO Channel 04/0 : 1[2] -> 2[3] via P2P/CUMEM
machine1:3161698:3161994 [2] NCCL INFO Channel 05/0 : 1[2] -> 2[3] via P2P/CUMEM
machine1:3161698:3161994 [2] NCCL INFO Channel 06/0 : 1[2] -> 2[3] via P2P/CUMEM
machine1:3161698:3161994 [2] NCCL INFO Channel 07/0 : 1[2] -> 2[3] via P2P/CUMEM
machine1:3161698:3161994 [2] NCCL INFO Channel 08/0 : 1[2] -> 2[3] via P2P/CUMEM
machine1:3161698:3161994 [2] NCCL INFO Channel 09/0 : 1[2] -> 2[3] via P2P/CUMEM
machine1:3161698:3161994 [2] NCCL INFO Channel 10/0 : 1[2] -> 2[3] via P2P/CUMEM
machine1:3161698:3161994 [2] NCCL INFO Channel 11/0 : 1[2] -> 2[3] via P2P/CUMEM
machine1:3161698:3161994 [2] NCCL INFO Channel 12/0 : 1[2] -> 2[3] via P2P/CUMEM
machine1:3161698:3161994 [2] NCCL INFO Channel 13/0 : 1[2] -> 2[3] via P2P/CUMEM
machine1:3161698:3161994 [2] NCCL INFO Channel 14/0 : 1[2] -> 2[3] via P2P/CUMEM
machine1:3161698:3161994 [2] NCCL INFO Channel 15/0 : 1[2] -> 2[3] via P2P/CUMEM
machine1:3161698:3161994 [2] NCCL INFO Connected all rings
machine1:3161699:3162003 [3] NCCL INFO Connected all rings
machine1:3161697:3161993 [1] NCCL INFO Connected all rings
machine1:3161699:3162003 [3] NCCL INFO Channel 00/0 : 2[3] -> 1[2] via P2P/CUMEM
machine1:3161699:3162003 [3] NCCL INFO Channel 01/0 : 2[3] -> 1[2] via P2P/CUMEM
machine1:3161699:3162003 [3] NCCL INFO Channel 02/0 : 2[3] -> 1[2] via P2P/CUMEM
machine1:3161699:3162003 [3] NCCL INFO Channel 03/0 : 2[3] -> 1[2] via P2P/CUMEM
machine1:3161698:3161994 [2] NCCL INFO Channel 00/0 : 1[2] -> 0[1] via P2P/CUMEM
machine1:3161699:3162003 [3] NCCL INFO Channel 04/0 : 2[3] -> 1[2] via P2P/CUMEM
machine1:3161697:3161993 [1] NCCL INFO Channel 04/0 : 0[1] -> 2[3] via P2P/CUMEM
machine1:3161698:3161994 [2] NCCL INFO Channel 01/0 : 1[2] -> 0[1] via P2P/CUMEM
machine1:3161699:3162003 [3] NCCL INFO Channel 05/0 : 2[3] -> 1[2] via P2P/CUMEM
machine1:3161697:3161993 [1] NCCL INFO Channel 05/0 : 0[1] -> 2[3] via P2P/CUMEM
machine1:3161698:3161994 [2] NCCL INFO Channel 02/0 : 1[2] -> 0[1] via P2P/CUMEM
machine1:3161699:3162003 [3] NCCL INFO Channel 08/0 : 2[3] -> 1[2] via P2P/CUMEM
machine1:3161697:3161993 [1] NCCL INFO Channel 06/0 : 0[1] -> 2[3] via P2P/CUMEM
machine1:3161698:3161994 [2] NCCL INFO Channel 03/0 : 1[2] -> 0[1] via P2P/CUMEM
machine1:3161699:3162003 [3] NCCL INFO Channel 09/0 : 2[3] -> 1[2] via P2P/CUMEM
machine1:3161697:3161993 [1] NCCL INFO Channel 07/0 : 0[1] -> 2[3] via P2P/CUMEM
machine1:3161698:3161994 [2] NCCL INFO Channel 06/0 : 1[2] -> 0[1] via P2P/CUMEM
machine1:3161699:3162003 [3] NCCL INFO Channel 10/0 : 2[3] -> 1[2] via P2P/CUMEM
machine1:3161697:3161993 [1] NCCL INFO Channel 12/0 : 0[1] -> 2[3] via P2P/CUMEM
machine1:3161698:3161994 [2] NCCL INFO Channel 07/0 : 1[2] -> 0[1] via P2P/CUMEM
machine1:3161699:3162003 [3] NCCL INFO Channel 11/0 : 2[3] -> 1[2] via P2P/CUMEM
machine1:3161697:3161993 [1] NCCL INFO Channel 13/0 : 0[1] -> 2[3] via P2P/CUMEM
machine1:3161698:3161994 [2] NCCL INFO Channel 08/0 : 1[2] -> 0[1] via P2P/CUMEM
machine1:3161699:3162003 [3] NCCL INFO Channel 12/0 : 2[3] -> 1[2] via P2P/CUMEM
machine1:3161697:3161993 [1] NCCL INFO Channel 14/0 : 0[1] -> 2[3] via P2P/CUMEM
machine1:3161698:3161994 [2] NCCL INFO Channel 09/0 : 1[2] -> 0[1] via P2P/CUMEM
machine1:3161699:3162003 [3] NCCL INFO Channel 13/0 : 2[3] -> 1[2] via P2P/CUMEM
machine1:3161697:3161993 [1] NCCL INFO Channel 15/0 : 0[1] -> 2[3] via P2P/CUMEM
machine1:3161698:3161994 [2] NCCL INFO Channel 10/0 : 1[2] -> 0[1] via P2P/CUMEM
machine1:3161698:3161994 [2] NCCL INFO Channel 11/0 : 1[2] -> 0[1] via P2P/CUMEM
machine1:3161698:3161994 [2] NCCL INFO Channel 14/0 : 1[2] -> 0[1] via P2P/CUMEM
machine1:3161698:3161994 [2] NCCL INFO Channel 15/0 : 1[2] -> 0[1] via P2P/CUMEM
machine1:3161697:3161993 [1] NCCL INFO Connected all trees
machine1:3161699:3162003 [3] NCCL INFO Connected all trees
machine1:3161698:3161994 [2] NCCL INFO Connected all trees
machine1:3161697:3161993 [1] NCCL INFO NVLS comm 0x9f17170 headRank 0 nHeads 3 buffSize 4194304 memSize 2097152 nvlsPerRankSize 201326592 nvlsTotalSize 603979776
machine1:3161699:3162003 [3] NCCL INFO NVLS comm 0xacbbbc0 headRank 2 nHeads 3 buffSize 4194304 memSize 2097152 nvlsPerRankSize 201326592 nvlsTotalSize 603979776
machine1:3161698:3161994 [2] NCCL INFO NVLS comm 0xaf7e0e0 headRank 1 nHeads 3 buffSize 4194304 memSize 2097152 nvlsPerRankSize 201326592 nvlsTotalSize 603979776
machine1:3161698:3161994 [2] NCCL INFO Connected NVLS tree
machine1:3161698:3161994 [2] NCCL INFO threadThresholds 8/8/64 | 24/8/64 | 512 | 512
machine1:3161698:3161994 [2] NCCL INFO 16 coll channels, 16 nvls channels, 16 p2p channels, 16 p2p channels per peer
machine1:3161699:3162003 [3] NCCL INFO Connected NVLS tree
machine1:3161699:3162003 [3] NCCL INFO threadThresholds 8/8/64 | 24/8/64 | 512 | 512
machine1:3161699:3162003 [3] NCCL INFO 16 coll channels, 16 nvls channels, 16 p2p channels, 16 p2p channels per peer
machine1:3161697:3161993 [1] NCCL INFO Connected NVLS tree
machine1:3161697:3161993 [1] NCCL INFO threadThresholds 8/8/64 | 24/8/64 | 512 | 512
machine1:3161697:3161993 [1] NCCL INFO 16 coll channels, 16 nvls channels, 16 p2p channels, 16 p2p channels per peer
machine1:3161697:3161993 [1] NCCL INFO comm 0x9f17170 rank 0 nranks 3 cudaDev 1 nvmlDev 1 busId 3a000 commId 0xd70f0ec1db0d96b9 - Init COMPLETE
machine1:3161698:3161994 [2] NCCL INFO comm 0xaf7e0e0 rank 1 nranks 3 cudaDev 2 nvmlDev 2 busId 4b000 commId 0xd70f0ec1db0d96b9 - Init COMPLETE
machine1:3161699:3162003 [3] NCCL INFO comm 0xacbbbc0 rank 2 nranks 3 cudaDev 3 nvmlDev 3 busId 5c000 commId 0xd70f0ec1db0d96b9 - Init COMPLETE
Batch 0: total=0.001s, sync=0.000s
Batch 1: total=0.001s, sync=0.000s
Batch 2: total=0.001s, sync=0.000s
Batch 3: total=0.001s, sync=0.000s
Batch 4: total=0.001s, sync=0.000s
Batch 5: total=0.001s, sync=0.000s
Batch 6: total=0.001s, sync=0.000s
Batch 7: total=0.001s, sync=0.000s
Batch 8: total=0.001s, sync=0.000s
Batch 9: total=0.002s, sync=0.000s
Batch 10: total=0.001s, sync=0.000s
Batch 11: total=0.001s, sync=0.000s
Batch 12: total=0.001s, sync=0.000s
Batch 13: total=0.001s, sync=0.000s
Batch 14: total=0.001s, sync=0.000s
Batch 15: total=0.001s, sync=0.000s
Batch 16: total=0.001s, sync=0.000s
Batch 17: total=0.001s, sync=0.000s
Batch 18: total=0.001s, sync=0.000s
Batch 19: total=0.001s, sync=0.000s
GPUs [1, 2, 3]: Average batch time: 0.001s
machine1:3161697:3162036 [1] NCCL INFO [Service thread] Connection closed by localRank 2
machine1:3161698:3162035 [2] NCCL INFO [Service thread] Connection closed by localRank 2
machine1:3161699:3162034 [3] NCCL INFO [Service thread] Connection closed by localRank 2
machine1:3161698:3162035 [2] NCCL INFO [Service thread] Connection closed by localRank 1
machine1:3161697:3162036 [1] NCCL INFO [Service thread] Connection closed by localRank 0
machine1:3161699:3162034 [3] NCCL INFO [Service thread] Connection closed by localRank 1
machine1:3161698:3162035 [2] NCCL INFO [Service thread] Connection closed by localRank 0
machine1:3161697:3162036 [1] NCCL INFO [Service thread] Connection closed by localRank 1
machine1:3161699:3162034 [3] NCCL INFO [Service thread] Connection closed by localRank 0
machine1:3161698:3161698 [2] NCCL INFO comm 0xaf7e0e0 rank 1 nranks 3 cudaDev 2 busId 4b000 - Abort COMPLETE
machine1:3161697:3161697 [1] NCCL INFO comm 0x9f17170 rank 0 nranks 3 cudaDev 1 busId 3a000 - Abort COMPLETE
machine1:3161699:3161699 [3] NCCL INFO comm 0xacbbbc0 rank 2 nranks 3 cudaDev 3 busId 5c000 - Abort COMPLETE

=== Testing GPUs: [2, 3] ===
machine1:3162076:3162076 [2] NCCL INFO Bootstrap : Using bond0:10.0.1.3<0>
machine1:3162076:3162076 [2] NCCL INFO NET/Plugin : dlerror=libnccl-net.so: cannot open shared object file: No such file or directory No plugin found (libnccl-net.so), using internal implementation
machine1:3162076:3162076 [2] NCCL INFO cudaDriverVersion 12040
NCCL version 2.19.3+cuda12.3
machine1:3162077:3162077 [3] NCCL INFO cudaDriverVersion 12040
machine1:3162077:3162077 [3] NCCL INFO Bootstrap : Using bond0:10.0.1.3<0>
machine1:3162077:3162077 [3] NCCL INFO NET/Plugin : dlerror=libnccl-net.so: cannot open shared object file: No such file or directory No plugin found (libnccl-net.so), using internal implementation
machine1:3162076:3162289 [2] NCCL INFO Failed to open libibverbs.so[.1]
machine1:3162076:3162289 [2] NCCL INFO NET/Socket : Using [0]bond0:10.0.1.3<0> [1]mlx5_ib8:100.96.1.37<0> [2]br-50bfd479b9ba:172.18.0.1<0> [3]br-46cbd64e257e:172.19.0.1<0> [4]br-4b73fd77a4e2:172.20.0.1<0> [5]br-af231856873e:172.21.0.1<0> [6]veth3da8205:fe80::90a1:70ff:fe01:a48a%veth3da8205<0> [7]veth95142d3:fe80::18e4:c2ff:fe4f:39a2%veth95142d3<0> [8]veth16999c4:fe80::38b4:bff:fecd:2ff2%veth16999c4<0> [9]vethd4fe970:fe80::88a6:6bff:fe12:d38c%vethd4fe970<0> [10]veth50d667b:fe80::34ac:36ff:fecb:c008%veth50d667b<0> [11]veth8b67a80:fe80::88b8:55ff:fe38:1741%veth8b67a80<0> [12]vethc492176:fe80::90fe:b1ff:feab:4c6b%vethc492176<0> [13]veth4a62d7c:fe80::e0b9:2ff:fef8:477%veth4a62d7c<0> [14]veth4d793c5:fe80::1cd3:67ff:feb5:ea%veth4d793c5<0> [15]veth70f9c41:fe80::1820:73ff:fe68:53fe%veth70f9c41<0>
machine1:3162076:3162289 [2] NCCL INFO Using non-device net plugin version 0
machine1:3162076:3162289 [2] NCCL INFO Using network Socket
machine1:3162077:3162298 [3] NCCL INFO Failed to open libibverbs.so[.1]
machine1:3162077:3162298 [3] NCCL INFO NET/Socket : Using [0]bond0:10.0.1.3<0> [1]mlx5_ib8:100.96.1.37<0> [2]br-50bfd479b9ba:172.18.0.1<0> [3]br-46cbd64e257e:172.19.0.1<0> [4]br-4b73fd77a4e2:172.20.0.1<0> [5]br-af231856873e:172.21.0.1<0> [6]veth3da8205:fe80::90a1:70ff:fe01:a48a%veth3da8205<0> [7]veth95142d3:fe80::18e4:c2ff:fe4f:39a2%veth95142d3<0> [8]veth16999c4:fe80::38b4:bff:fecd:2ff2%veth16999c4<0> [9]vethd4fe970:fe80::88a6:6bff:fe12:d38c%vethd4fe970<0> [10]veth50d667b:fe80::34ac:36ff:fecb:c008%veth50d667b<0> [11]veth8b67a80:fe80::88b8:55ff:fe38:1741%veth8b67a80<0> [12]vethc492176:fe80::90fe:b1ff:feab:4c6b%vethc492176<0> [13]veth4a62d7c:fe80::e0b9:2ff:fef8:477%veth4a62d7c<0> [14]veth4d793c5:fe80::1cd3:67ff:feb5:ea%veth4d793c5<0> [15]veth70f9c41:fe80::1820:73ff:fe68:53fe%veth70f9c41<0>
machine1:3162077:3162298 [3] NCCL INFO Using non-device net plugin version 0
machine1:3162077:3162298 [3] NCCL INFO Using network Socket
machine1:3162077:3162298 [3] NCCL INFO comm 0x99ccb20 rank 1 nranks 2 cudaDev 3 nvmlDev 3 busId 5c000 commId 0x9b2b9a19786b74fd - Init START
machine1:3162076:3162289 [2] NCCL INFO comm 0xa5ff900 rank 0 nranks 2 cudaDev 2 nvmlDev 2 busId 4b000 commId 0x9b2b9a19786b74fd - Init START
machine1:3162077:3162298 [3] NCCL INFO Setting affinity for GPU 3 to ff,ffffffff,ffff0000,00000000,00ffffff,ffffffff
machine1:3162076:3162289 [2] NCCL INFO Setting affinity for GPU 2 to ff,ffffffff,ffff0000,00000000,00ffffff,ffffffff
machine1:3162077:3162298 [3] NCCL INFO Trees [0] -1/-1/-1->1->0 [1] -1/-1/-1->1->0 [2] -1/-1/-1->1->0 [3] -1/-1/-1->1->0 [4] 0/-1/-1->1->-1 [5] 0/-1/-1->1->-1 [6] 0/-1/-1->1->-1 [7] 0/-1/-1->1->-1 [8] -1/-1/-1->1->0 [9] -1/-1/-1->1->0 [10] -1/-1/-1->1->0 [11] -1/-1/-1->1->0 [12] 0/-1/-1->1->-1 [13] 0/-1/-1->1->-1 [14] 0/-1/-1->1->-1 [15] 0/-1/-1->1->-1
machine1:3162077:3162298 [3] NCCL INFO P2P Chunksize set to 524288
machine1:3162076:3162289 [2] NCCL INFO Channel 00/16 :    0   1
machine1:3162076:3162289 [2] NCCL INFO Channel 01/16 :    0   1
machine1:3162076:3162289 [2] NCCL INFO Channel 02/16 :    0   1
machine1:3162076:3162289 [2] NCCL INFO Channel 03/16 :    0   1
machine1:3162076:3162289 [2] NCCL INFO Channel 04/16 :    0   1
machine1:3162076:3162289 [2] NCCL INFO Channel 05/16 :    0   1
machine1:3162076:3162289 [2] NCCL INFO Channel 06/16 :    0   1
machine1:3162076:3162289 [2] NCCL INFO Channel 07/16 :    0   1
machine1:3162076:3162289 [2] NCCL INFO Channel 08/16 :    0   1
machine1:3162076:3162289 [2] NCCL INFO Channel 09/16 :    0   1
machine1:3162076:3162289 [2] NCCL INFO Channel 10/16 :    0   1
machine1:3162076:3162289 [2] NCCL INFO Channel 11/16 :    0   1
machine1:3162076:3162289 [2] NCCL INFO Channel 12/16 :    0   1
machine1:3162076:3162289 [2] NCCL INFO Channel 13/16 :    0   1
machine1:3162076:3162289 [2] NCCL INFO Channel 14/16 :    0   1
machine1:3162076:3162289 [2] NCCL INFO Channel 15/16 :    0   1
machine1:3162076:3162289 [2] NCCL INFO Trees [0] 1/-1/-1->0->-1 [1] 1/-1/-1->0->-1 [2] 1/-1/-1->0->-1 [3] 1/-1/-1->0->-1 [4] -1/-1/-1->0->1 [5] -1/-1/-1->0->1 [6] -1/-1/-1->0->1 [7] -1/-1/-1->0->1 [8] 1/-1/-1->0->-1 [9] 1/-1/-1->0->-1 [10] 1/-1/-1->0->-1 [11] 1/-1/-1->0->-1 [12] -1/-1/-1->0->1 [13] -1/-1/-1->0->1 [14] -1/-1/-1->0->1 [15] -1/-1/-1->0->1
machine1:3162076:3162289 [2] NCCL INFO P2P Chunksize set to 524288
machine1:3162077:3162298 [3] NCCL INFO Channel 00/0 : 1[3] -> 0[2] via P2P/CUMEM
machine1:3162077:3162298 [3] NCCL INFO Channel 01/0 : 1[3] -> 0[2] via P2P/CUMEM
machine1:3162077:3162298 [3] NCCL INFO Channel 02/0 : 1[3] -> 0[2] via P2P/CUMEM
machine1:3162077:3162298 [3] NCCL INFO Channel 03/0 : 1[3] -> 0[2] via P2P/CUMEM
machine1:3162077:3162298 [3] NCCL INFO Channel 04/0 : 1[3] -> 0[2] via P2P/CUMEM
machine1:3162077:3162298 [3] NCCL INFO Channel 05/0 : 1[3] -> 0[2] via P2P/CUMEM
machine1:3162077:3162298 [3] NCCL INFO Channel 06/0 : 1[3] -> 0[2] via P2P/CUMEM
machine1:3162077:3162298 [3] NCCL INFO Channel 07/0 : 1[3] -> 0[2] via P2P/CUMEM
machine1:3162077:3162298 [3] NCCL INFO Channel 08/0 : 1[3] -> 0[2] via P2P/CUMEM
machine1:3162077:3162298 [3] NCCL INFO Channel 09/0 : 1[3] -> 0[2] via P2P/CUMEM
machine1:3162077:3162298 [3] NCCL INFO Channel 10/0 : 1[3] -> 0[2] via P2P/CUMEM
machine1:3162077:3162298 [3] NCCL INFO Channel 11/0 : 1[3] -> 0[2] via P2P/CUMEM
machine1:3162077:3162298 [3] NCCL INFO Channel 12/0 : 1[3] -> 0[2] via P2P/CUMEM
machine1:3162077:3162298 [3] NCCL INFO Channel 13/0 : 1[3] -> 0[2] via P2P/CUMEM
machine1:3162077:3162298 [3] NCCL INFO Channel 14/0 : 1[3] -> 0[2] via P2P/CUMEM
machine1:3162077:3162298 [3] NCCL INFO Channel 15/0 : 1[3] -> 0[2] via P2P/CUMEM
machine1:3162076:3162289 [2] NCCL INFO Channel 00/0 : 0[2] -> 1[3] via P2P/CUMEM
machine1:3162076:3162289 [2] NCCL INFO Channel 01/0 : 0[2] -> 1[3] via P2P/CUMEM
machine1:3162076:3162289 [2] NCCL INFO Channel 02/0 : 0[2] -> 1[3] via P2P/CUMEM
machine1:3162076:3162289 [2] NCCL INFO Channel 03/0 : 0[2] -> 1[3] via P2P/CUMEM
machine1:3162076:3162289 [2] NCCL INFO Channel 04/0 : 0[2] -> 1[3] via P2P/CUMEM
machine1:3162076:3162289 [2] NCCL INFO Channel 05/0 : 0[2] -> 1[3] via P2P/CUMEM
machine1:3162076:3162289 [2] NCCL INFO Channel 06/0 : 0[2] -> 1[3] via P2P/CUMEM
machine1:3162076:3162289 [2] NCCL INFO Channel 07/0 : 0[2] -> 1[3] via P2P/CUMEM
machine1:3162076:3162289 [2] NCCL INFO Channel 08/0 : 0[2] -> 1[3] via P2P/CUMEM
machine1:3162076:3162289 [2] NCCL INFO Channel 09/0 : 0[2] -> 1[3] via P2P/CUMEM
machine1:3162076:3162289 [2] NCCL INFO Channel 10/0 : 0[2] -> 1[3] via P2P/CUMEM
machine1:3162076:3162289 [2] NCCL INFO Channel 11/0 : 0[2] -> 1[3] via P2P/CUMEM
machine1:3162076:3162289 [2] NCCL INFO Channel 12/0 : 0[2] -> 1[3] via P2P/CUMEM
machine1:3162076:3162289 [2] NCCL INFO Channel 13/0 : 0[2] -> 1[3] via P2P/CUMEM
machine1:3162076:3162289 [2] NCCL INFO Channel 14/0 : 0[2] -> 1[3] via P2P/CUMEM
machine1:3162076:3162289 [2] NCCL INFO Channel 15/0 : 0[2] -> 1[3] via P2P/CUMEM
machine1:3162077:3162298 [3] NCCL INFO Connected all rings
machine1:3162076:3162289 [2] NCCL INFO Connected all rings
machine1:3162077:3162298 [3] NCCL INFO Connected all trees
machine1:3162076:3162289 [2] NCCL INFO Connected all trees
machine1:3162077:3162298 [3] NCCL INFO threadThresholds 8/8/64 | 16/8/64 | 512 | 512
machine1:3162077:3162298 [3] NCCL INFO 16 coll channels, 0 nvls channels, 16 p2p channels, 16 p2p channels per peer
machine1:3162076:3162289 [2] NCCL INFO threadThresholds 8/8/64 | 16/8/64 | 512 | 512
machine1:3162076:3162289 [2] NCCL INFO 16 coll channels, 0 nvls channels, 16 p2p channels, 16 p2p channels per peer
machine1:3162077:3162298 [3] NCCL INFO comm 0x99ccb20 rank 1 nranks 2 cudaDev 3 nvmlDev 3 busId 5c000 commId 0x9b2b9a19786b74fd - Init COMPLETE
machine1:3162076:3162289 [2] NCCL INFO comm 0xa5ff900 rank 0 nranks 2 cudaDev 2 nvmlDev 2 busId 4b000 commId 0x9b2b9a19786b74fd - Init COMPLETE
Batch 0: total=0.001s, sync=0.000s
Batch 1: total=0.001s, sync=0.000s
Batch 2: total=0.001s, sync=0.000s
Batch 3: total=0.001s, sync=0.000s
Batch 4: total=0.001s, sync=0.000s
Batch 5: total=0.001s, sync=0.000s
Batch 6: total=0.001s, sync=0.000s
Batch 7: total=0.001s, sync=0.000s
Batch 8: total=0.001s, sync=0.000s
Batch 9: total=0.001s, sync=0.000s
Batch 10: total=0.001s, sync=0.000s
Batch 11: total=0.001s, sync=0.000s
Batch 12: total=0.001s, sync=0.000s
Batch 13: total=0.001s, sync=0.000s
Batch 14: total=0.001s, sync=0.000s
Batch 15: total=0.001s, sync=0.000s
Batch 16: total=0.001s, sync=0.000s
Batch 17: total=0.001s, sync=0.000s
Batch 18: total=0.001s, sync=0.000s
Batch 19: total=0.001s, sync=0.000s
GPUs [2, 3]: Average batch time: 0.001s
machine1:3162076:3162320 [2] NCCL INFO [Service thread] Connection closed by localRank 1
machine1:3162077:3162319 [3] NCCL INFO [Service thread] Connection closed by localRank 1
machine1:3162076:3162320 [2] NCCL INFO [Service thread] Connection closed by localRank 0
machine1:3162077:3162319 [3] NCCL INFO [Service thread] Connection closed by localRank 0
machine1:3162076:3162076 [2] NCCL INFO comm 0xa5ff900 rank 0 nranks 2 cudaDev 2 busId 4b000 - Abort COMPLETE
machine1:3162077:3162077 [3] NCCL INFO comm 0x99ccb20 rank 1 nranks 2 cudaDev 3 busId 5c000 - Abort COMPLETE

"""