{
    // 使用 IntelliSense 了解相关属性。 
    // 悬停以查看现有属性的描述。
    // 欲了解更多信息，请访问: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Debug: Validate Models (Small Dataset)",
            "type": "python",
            "request": "launch",
            "program": "${workspaceFolder}/robotprm/scripts/validate_models.py",
            "args": [
                "--model_path", "/mnt/disk2/surui/data/Experiment_data/PRMs/rtg_dense_lr3e4_20250716/rtg_unified_checkpoint_epoch_5.pt",
                "--method", "rtg",
                "--batch_size", "256",
                // "--max_episodes", "None",
                // "--hidden_size", "512",
                // "--num_layers", "6",
                // "--reward_type", "sparse",
                "--output_dir", "/mnt/disk2/surui/BoostingVLA/"
            ],
            "console": "integratedTerminal",
            "justMyCode": true,
            "cwd": "${workspaceFolder}/robotprm",
            "env": {
                "CUDA_VISIBLE_DEVICES": "0"
            }
        },
        {
            "name": "Debug: IRL Training (Single GPU)",
            "type": "python",
            "request": "launch",
            "program": "${workspaceFolder}/robotprm/scripts/train_unified.py",
            "args": [
                "--world_size", "1",
                "--per_gpu_bs", "256",
                "--total_epochs", "30",
                "--method", "irl",
                "--port", "12371",
                "--gcl_weight", "0.1",
                "--preference_weight", "0.8",
                "--rtg_weight", "0.1",
                "--debug_only",
                "--debug_max_episodes", "100"
            ],
            "console": "integratedTerminal",
            "justMyCode": true,
            "cwd": "${workspaceFolder}/robotprm",
            "env": {
                "CUDA_VISIBLE_DEVICES": "0",
                "OMP_NUM_THREADS": "4",
                "NCCL_DEBUG": "WARN",
                "PYTORCH_CUDA_ALLOC_CONF": "max_split_size_mb:512",
                "PYTHONDONTWRITEBYTECODE": "1",
                "PYTHONUNBUFFERED": "1"
            }
        },
        {
            "name": "Debug: RTG Training (Single GPU) - Script Equivalent",
            "type": "python",
            "request": "launch",
            "program": "${workspaceFolder}/robotprm/scripts/train_unified.py",
            "args": [
                "--method", "rtg",
                "--world_size", "4",
                "--per_gpu_bs", "128",
                "--total_epochs", "30",
                // "--hidden_size", "384",  // RTG默认值
                // "--num_layers", "12",    // RTG默认值
                // "--sequence_length", "10", // RTG默认值
                // "--learning_rate_q", "3e-4",
                // "--reward_type", "dense",
                // "--sparse_reward_steps", "1",
                // "--val_freq", "1",
                "--port", "12371",  // RTG脚本的默认端口
                "--debug_only",
                // "--debug_max_episodes", "500",
                // RTG特定参数
                "--rtg_weight", "0",
                "--preference_weight", "1.0", 
                "--gcl_weight", "0",
                // "--rtg_tau", "0.7",
            ],
            "console": "integratedTerminal",
            "justMyCode": true,
            "cwd": "${workspaceFolder}/robotprm",
            "env": {
                "CUDA_VISIBLE_DEVICES": "0,1,2,3",
                "OMP_NUM_THREADS": "4",
                "NCCL_DEBUG": "WARN",
                "PYTORCH_CUDA_ALLOC_CONF": "max_split_size_mb:512",
                "PYTHONDONTWRITEBYTECODE": "1",
                "PYTHONUNBUFFERED": "1"
            }
        },
        {
            "name": "Debug Online RL Training",
            "type": "python",
            "request": "launch",
            "program": "${workspaceFolder}/robotprm/scripts/train_online_rl.py",
            "args": [
                "--config", "${workspaceFolder}/robotprm/configs/online_rl_config.yaml",
                "--policy-path", "/mnt/disk2/surui/BoostingVLA/GR-1/logs/snapshot_ABC.pt",
                "--task-name", "move_slider_left",
                "--num-episodes", "5",
                "--device", "cpu",
                "--log-level", "DEBUG"
            ],
            "console": "integratedTerminal",
            "cwd": "${workspaceFolder}/robotprm",
            "env": {
                "CUDA_VISIBLE_DEVICES": "0"
            },
            "stopOnEntry": false,
            "justMyCode": true
        }
    ]
}