import subprocess
import os

def run_git_command(cmd):
    """运行git命令并返回结果"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, cwd='/mnt/disk2/surui/BoostingVLA/robotprm')
        return result.stdout.strip()
    except Exception as e:
        return f"Error: {e}"

def check_recent_commits():
    """检查最近的提交"""
    print("=== 最近5次提交 ===")
    commits = run_git_command("git log --oneline -5")
    print(commits)
    
    print("\n=== 最近提交的详细信息 ===")
    latest_commit = run_git_command("git log -1 --stat")
    print(latest_commit)
    
    print("\n=== 最近提交的具体更改 ===")
    diff = run_git_command("git show HEAD --name-only")
    print(diff)
    
    print("\n=== 检查trainer相关文件的最近更改 ===")
    trainer_changes = run_git_command("git log --oneline -10 --grep='loss\\|config\\|trainer'")
    print(trainer_changes)

if __name__ == "__main__":
    check_recent_commits()

"""
(prm) (base) surui@machine1:~/BoostingVLA$ /mnt/disk2/surui/miniconda3/envs/prm/bin/python /mnt/disk2/surui/BoostingVLA/check_recent_commits.py
=== 最近5次提交 ===
f3e19e9 add per gpu bs
0b47ad8 fix bug
f5f4ff6 fix bug
72b126b refractor configs & use loss weight to control the loss function use
7b81941 update

=== 最近提交的详细信息 ===
commit f3e19e9a2a35298bbdad987a00541740975d6d9d
Author: CCCalcifer <<EMAIL>>
Date:   Thu Jul 17 01:27:11 2025 +0800

    add per gpu bs

 scripts/train_unified.py | 14 +++++++++-----
 1 file changed, 9 insertions(+), 5 deletions(-)

=== 最近提交的具体更改 ===
commit f3e19e9a2a35298bbdad987a00541740975d6d9d
Author: CCCalcifer <<EMAIL>>
Date:   Thu Jul 17 01:27:11 2025 +0800

    add per gpu bs

scripts/train_unified.py

=== 检查trainer相关文件的最近更改 ===
72b126b refractor configs & use loss weight to control the loss function use
a47ced5 print config
d14457d refractor whole code. align to IRL trainer
b5927cd rename config (remove gcl. use preference & rtg)
4d139e0 add dual loss
3ec7d6a loss module
f91bd77 remove curriculum learning & change GCL loss to Preference loss
081c51b add loss func
805f36c In ranking consistency calculate, change '>=' to '>'. Otherwise it make RC=1 if outputs are the same; Add curriculum learning in RTG trainer
511081a Implement STRICT MODE for RTG training - remove all fallback mechanisms

"""